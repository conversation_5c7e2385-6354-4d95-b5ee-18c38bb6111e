using SM.Service.Contents.Contracts;
using SM.Service.Message.DTOs;

namespace SM.Service.Message.Implementations;

public class MessageService : SMServiceBase, IMessageService
{
    public MessageService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }


    public async Task<SendMessageRespose> SendMessage(SendMessageRequest request)
    {
        var newMessage = new Data.Entities.Message.Message
        {
            IsDeleted = false,
            CreatedAt = DateTime.Now,
            ModifiedAt = null,
            DeletedAt = null,
            FullName = request.FullName,
            Email = request.Email,
            Phone = null,
            Text = request.Message
        };

        await DbContext.AddAsync(newMessage);

        await DbContext.SaveChangesAsync();

        return new SendMessageRespose
        {
            IsSuccess = newMessage.Id > 0,
            ResultMsg = null
        };
    }
}