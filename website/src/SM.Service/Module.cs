using Microsoft.Extensions.DependencyInjection;
using SM.Service.Catalog.Contracts;
using SM.Service.Catalog.Implementations;
using SM.Service.Contents.Contracts;
using SM.Service.Contents.Implementations;
using SM.Service.Message.Implementations;

namespace SM.Service;

public static class Module
{
    public static IServiceCollection AddServiceModule(this IServiceCollection collection)
    {
        collection.AddScoped<IContentService, ContentService>();
        collection.AddScoped<ICatalogService, CatalogService>();
        collection.AddScoped<IMessageService, MessageService>();

        return collection;
    }
}