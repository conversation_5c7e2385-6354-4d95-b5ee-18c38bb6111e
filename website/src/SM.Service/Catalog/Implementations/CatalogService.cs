using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using SM.Data.Entities.Catalog;
using SM.Data.Entities.Navigation;
using SM.Service.Catalog.Contracts;
using SM.Service.Catalog.DTOs;
using SM.Service.Catalog.Exceptions;
using SM.Service.Contents.Contracts;
using SM.Service.Contents.DTOs;
using SM.Service.Helper;

namespace SM.Service.Catalog.Implementations;

public class CatalogService : SMServiceBase, ICatalogService
{
    private readonly IContentService _contentService;

    public CatalogService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
        _contentService = ServiceProvider.GetRequiredService<IContentService>();
    }

    public async Task<GetProductsResponse> GetProducts(GetProductsRequest request)
    {
        //var categoryId = Utils.GetIdFormSlug(request.Slug);

        var categoryTranslation = await DbContext.ProductCategoryTranslations
            .Where(w =>
                !w.IsDeleted &&
                w.Category.IsActive &&
                w.Slug.Equals(request.Slug))
            .AsNoTracking()
            .FirstOrDefaultAsync();

        if (categoryTranslation == null)
        {
            throw new NotFoundCategoryException("Not Found Category");
        }

        var categoryId = categoryTranslation.CategoryId;

        var productCategoryRelations = await DbContext.ProductCategoryRelations
            .Include(i => i.Product)
                .ThenInclude(i => i.ProductImages)
            .Include(i => i.Product)
                .ThenInclude(i => i.ProductTranslations)
            .Include(i => i.Category)
            .Where(w =>
                w.Product.IsActive && !w.Product.IsDeleted &&
                w.CategoryId == categoryId
            )
            .AsNoTracking()
            .ToListAsync();

        var products = productCategoryRelations.Select(s=>s.Product);

        var productDtoList = new List<ProductListItemDto>();
        foreach (var product in products)
        {
            var productTranslation=product.ProductTranslations.FirstOrDefault(f => f.LangPrefix == request.CurrentCultureInfo);
            if (productTranslation == null)
            {
                continue;
            }

            var imagePath = product.ProductImages.FirstOrDefault(f => f.IsActive && !f.IsDeleted && f.IsList)?.Path;
            var detailUrl = Utils.PrepareProductDetailUrl(request.CurrentCultureInfo, productTranslation.Slug,
                Utils.GetLocalizationRoutingData(request.CurrentCultureInfo));
            productDtoList.Add(new ProductListItemDto
            {
                ImagePath = imagePath,
                DetailUrl = detailUrl,
                Caption = productTranslation.Caption
            });

        }

        var productCategory=productCategoryRelations.Select(s => s.Category).FirstOrDefault();
        
        var categoryTranslations = await DbContext.ProductCategoryTranslations
            .Where(w =>
                !w.IsDeleted &&
                w.Category.IsActive &&
                !w.Category.IsDeleted &&
                w.CategoryId == categoryId)
            .AsNoTracking()
            .ToListAsync();

        var category = categoryTranslations.Where(w =>
                w.LangPrefix == request.CurrentCultureInfo &&
                w.Slug == request.Slug)
            .Select(s => new ProductCategoryDto
            {
                Caption = s.Caption,
                Slug = "",
                BannerImagePath = s.BannerImagePath,
                Description = s.Description,
                SeoTitle = s.SeoTitle,
                SeoDescription = s.SeoDescription,
                SeoKeywords = s.SeoKeywords
            }).FirstOrDefault();

        var productCategories = await GetProductCategory(request.CurrentCultureInfo);

        var languages = await prepareLanguageUrl(request.CurrentCultureInfo, categoryTranslations);


        return new GetProductsResponse
        {
            Products = productDtoList,
            ProductCategories = productCategories,
            PageContent = new ProductsPageContent
            {
                Title = category.Caption,
                Description = category.Description,
                ImagePath = category.BannerImagePath,
                SeoTitle = category.SeoTitle,
                SeoDescription = category.SeoDescription,
                SeoKeywords = category.SeoKeywords,
            },
            Languages = languages
        };
    }

    public async Task<GetProductDetailResponse> GetProductDetail(GetProductDetailRequest request)
    {
        //var productId = Utils.GetIdFormSlug(request.Slug);

        var productTranslation = await DbContext.ProductTranslations.FirstOrDefaultAsync(f => !f.IsDeleted &&
            f.Product.IsActive &&
            f.LangPrefix == request.CurrentCultureInfo &&
            f.Slug.Equals(request.Slug));
        
        if (productTranslation == null)
        {
            throw new NotFoundProductException("Not Found Product");
        }

        var productId = productTranslation.ProductId;

        var product = await DbContext.Products
            .Include(i => i.ProductImages)
            .Include(i => i.ProductTranslations)
            .Where(w => w.Id == productId)
            .AsNoTracking()
            .FirstOrDefaultAsync();

        if (product == null)
        {
            throw new NotFoundProductException("Not Found Product");
        }

        var productTranslations = product.ProductTranslations
            .Where(w =>
                !w.IsDeleted
            ).ToList();

        var productImage = product.ProductImages
            .Where(w =>
                !w.IsDeleted &&
                w.IsActive
            )
            .Select(s => new ProductImageDto
            {
                ImagePath = s.Path,
                Id = s.Id
            }).ToList();

        var socialMediaAccountAddresses = await _contentService.GetSocialMediaAccountAddresses();

        return new GetProductDetailResponse
        {
            Detail = new ProductDetailDto
            {
                ProductImages = productImage,
                Title = productTranslation.Title,
                Caption = productTranslation.Caption,
                Description = productTranslation.Description,
                Ingredients = productTranslation.Ingredients,
                HowToApply = productTranslation.HowToApply,
                //SkinMoisture = productTranslation.SkinMoisture,
                Dimensions = productTranslation.Dimensions,
                Warnings = productTranslation.Warnings
            },
            PageContent = new ProductDetailPageContent()
            {
                SeoTitle = productTranslation.SeoTitle,
                SeoDescription = productTranslation.SeoDescription,
                SeoKeywords = productTranslation.SeoKeywords
            },
            SocialMediaAccountAddresses = socialMediaAccountAddresses,
            Languages = await prepareLanguageUrl(request.CurrentCultureInfo, productTranslations)
        };
    }

    public async Task<IEnumerable<ProductCategoryDto>> GetProductCategory(string CurrentCultureInfo)
    {
        return await DbContext.NavigationTranslations.Where(w =>
            w.Navigation.Type == NavigationType.Category &&
            w.LangPrefix == CurrentCultureInfo &&
            w.Navigation.IsActive && !w.Navigation.IsDeleted).Select(s =>
            new ProductCategoryDto
            {
                Caption = s.Caption,
                Slug = Utils.PrepareUrl(NavigationType.Category, CurrentCultureInfo, s.Link),
                BannerImagePath = null,
                Description = null,
                SortPriority = s.Navigation.SortPriority
            }).OrderBy(o => o.SortPriority).ToListAsync();
    }

    private async Task<IEnumerable<LanguageDto>> prepareLanguageUrl(string langPrefix,
        List<ProductCategoryTranslation> productTranslations)
    {
        var language = await _contentService.GetLanguages(langPrefix);

        var languageDtos = language.ToList();
        languageDtos.ForEach(fe =>
        {
            var productTransaction = productTranslations.FirstOrDefault(f => f.LangPrefix == fe.Prefix);

            // Use new SEO-friendly URL structure
            fe.Url = $"/{fe.Prefix}/{Utils.GetNewCategoryRoutingData(fe.Prefix)}/{productTransaction.Slug}";
        });

        return languageDtos;
    }

    private async Task<IEnumerable<LanguageDto>> prepareLanguageUrl(string langPrefix,
        List<ProductTranslation> productTranslations)
    {
        var language = await _contentService.GetLanguages(langPrefix);

        var languageDtos = language.ToList();
        languageDtos.ForEach(fe =>
        {
            var productTransaction = productTranslations.FirstOrDefault(f => f.LangPrefix == fe.Prefix);

            if (productTransaction != null)
            {
                // Get category slug for the new URL structure
                var categorySlug = GetCategorySlugForProduct(productTransaction.ProductId, fe.Prefix);

                // Use new SEO-friendly URL structure: /{lang}/urunler/{category-slug}/{product-slug}
                fe.Url = Utils.PrepareNewProductDetailUrl(fe.Prefix, categorySlug, productTransaction.Slug);
            }
        });

        return languageDtos;
    }

    private string GetCategorySlugForProduct(int productId, string langPrefix)
    {
        // Optimized query with join and AsNoTracking
        var categorySlug = (from pcr in DbContext.ProductCategoryRelations.AsNoTracking()
                           join pct in DbContext.ProductCategoryTranslations.AsNoTracking()
                               on pcr.CategoryId equals pct.CategoryId
                           where pcr.ProductId == productId && pct.LangPrefix == langPrefix
                           select pct.Slug)
                           .FirstOrDefault();

        return categorySlug ?? GetFallbackCategorySlug(langPrefix);
    }

    private static string GetFallbackCategorySlug(string langPrefix)
    {
        return langPrefix switch
        {
            "tr" => "genel",
            "en" => "general",
            "ru" => "общий",
            _ => "genel"
        };
    }
}