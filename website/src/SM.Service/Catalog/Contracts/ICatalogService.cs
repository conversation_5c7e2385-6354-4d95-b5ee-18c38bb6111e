using SM.Service.Catalog.DTOs;

namespace SM.Service.Catalog.Contracts;

public interface ICatalogService
{
    Task<GetProductsResponse> GetProducts(GetProductsRequest request);
    Task<GetProductDetailResponse> GetProductDetail(GetProductDetailRequest request);
    Task<IEnumerable<ProductCategoryDto>> GetProductCategory(string CurrentCultureInfo);
    Task<string> GetCategorySlugByProductSlugAsync(string productSlug, string langPrefix);
}