using Microsoft.Extensions.DependencyInjection;
using SM.Data;

namespace SM.Service;

public abstract class SMServiceBase
{
    protected readonly SMDbContext DbContext;
    protected readonly IServiceProvider ServiceProvider;


    protected SMServiceBase(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;

        DbContext = ServiceProvider.GetRequiredService<SMDbContext>();
    }
}