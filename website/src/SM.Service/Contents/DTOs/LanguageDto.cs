namespace SM.Service.Contents.DTOs;

public class LanguageDto
{
    public string CultureInfo { get; set; }
    public string Prefix { get; set; }
    public string Name { get; set; }
    public string ImagePath { get; set; }
    public bool IsSelected { get; set; }
    public string Url { get; set; }

    // Hreflang specific properties
    public string HreflangCode => GetHreflangCode();
    public string FullUrl => $"https://www.sabunmutfagi.com{Url}";

    private string GetHreflangCode()
    {
        return Prefix switch
        {
            "tr" => "tr-TR",
            "en" => "en-US",
            "ru" => "ru-RU",
            _ => "tr-TR"
        };
    }
}