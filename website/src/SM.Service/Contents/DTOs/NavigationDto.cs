using SM.Data.Entities.Navigation;

namespace SM.Service.Contents.DTOs;

public class NavigationDto
{
    public int Id { get; set; }
    public int ParentId { get; set; }
    public string Caption { get; set; }
    public string Code { get; set; }
    public string Url { get; set; }
    public int SortPriority { get; set; }

    public NavigationType Type { get; set; }
    public bool IsTargetBlank { get; set; }
}