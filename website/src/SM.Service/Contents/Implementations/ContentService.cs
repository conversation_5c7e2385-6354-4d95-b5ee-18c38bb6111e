using System.Text;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using SM.Data;
using SM.Data.Entities.Content;
using SM.Data.Entities.Navigation;
using SM.Service.Contents.Contracts;
using SM.Service.Contents.DTOs;
using SM.Service.Helper;

namespace SM.Service.Contents.Implementations;

public class ContentService : SMServiceBase, IContentService
{
    public ContentService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public async Task<IEnumerable<SocialMediaAccountAddressDto>> GetSocialMediaAccountAddresses()
    {
        var content = await DbContext.Contents
            .Where(w => w.Key == Constants.SOCIAL_ACCOUNT_ADDRESS_LIST)
            .AsNoTracking()
            .FirstOrDefaultAsync();

        return content != null
            ? JsonConvert.DeserializeObject<IEnumerable<SocialMediaAccountAddressDto>>(content.Value)
            : new List<SocialMediaAccountAddressDto>();
    }

    public async Task<IEnumerable<LanguageDto>> GetLanguages(string currentCultureInfo)
    {
        var languages = new List<LanguageDto>
        {
            new()
            {
                CultureInfo = "tr-TR",
                Prefix = "tr",
                Name = "Türkçe",
                ImagePath = "/images/flags/tr-TR.png",
                IsSelected = "tr" == currentCultureInfo,
                Url = "/tr"
            },
            new()
            {
                CultureInfo = "en-GB",
                Prefix = "en",
                Name = "English",
                ImagePath = "/images/flags/en-GB.png",
                IsSelected = "en" == currentCultureInfo,
                Url = "/en"
            },
            new()
            {
                CultureInfo = "ru-RU",
                Prefix = "ru",
                Name = "Русский",
                ImagePath = "/images/flags/ru-RU.png",
                IsSelected = "ru" == currentCultureInfo,
                Url = "/ru"
            }
            /*new LanguageDto
            {
                CultureInfo = "de-DE",
                Prefix = "de",
                Name = "Deutsch",
                ImagePath = "/images/flags/de-DE.png",
                IsSelected = "de"==currentCultureInfo,
                Url =  "/de"
            },
            new LanguageDto
            {
                CultureInfo = "ar-SA",
                Prefix = "ar",
                Name = "عربي",
                ImagePath = "/images/flags/ar-SA.png",
                IsSelected = "ar"==currentCultureInfo,
                Url =  "/ar"
            }*/
        };

        return languages;
    }

    public async Task<IEnumerable<NavigationDto>> GetNavigation(string currentCulterInfo)
    {
        return await DbContext.NavigationTranslations.Where(w =>
            w.Navigation.IsActive &&
            !w.Navigation.IsDeleted &&
            w.LangPrefix == currentCulterInfo).Select(s => new NavigationDto
        {
            Id = s.NavigationId,
            ParentId = s.Navigation.ParentId,
            Caption = s.Caption,
            Code = s.Navigation.Code,
            SortPriority = s.Navigation.SortPriority,
            IsTargetBlank = s.Navigation.Type == NavigationType.Out,
            Url = Utils.PrepareUrl(s.Navigation.Type, s.LangPrefix, s.Link)
        })
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<string> GetNavigationHTML(string currentCulterInfo)
    {
        var navigations = await GetNavigation(currentCulterInfo);

        var output = new StringBuilder();

        output.Append("<ul class=\"menu-container justify-content-between\">");
        AddNavigationLink(output, navigations);
        output.Append("</ul>");
        return output.ToString();
    }

    public async Task<IEnumerable<HomeSliderDto>> GetHomeSlider(string currentCultureInfo)
    {
        var sliders = await DbContext.Contents
            .Where(w => w.Key == Constants.HOME_SLIDER_ITEM_LIST)
            .AsNoTracking()
            .FirstOrDefaultAsync();

        var sliderItems = JsonConvert.DeserializeObject<IEnumerable<HomeSliderItem>>(sliders.Value);


        return sliderItems.Select(s => new HomeSliderDto
        {
            Title = s.Title[currentCultureInfo],
            Description = s.Description[currentCultureInfo],
            ImagePath = s.ImagePath[currentCultureInfo]
        }).ToList();
    }

    public async Task<GetAboutContentResponse> GetAboutContent(string currentCultureInfo)
    {
        var content = await DbContext.Contents
            .Where(w => w.Key == Constants.ABOUT_CONTENT)
            .AsNoTracking()
            .FirstOrDefaultAsync();

        var contentObj = JsonConvert.DeserializeObject<AboutContentDto>(content.Value);

        var language = await GetLanguages(currentCultureInfo);

        language.ToList().ForEach(fe =>
        {
            switch (fe.Prefix)
            {
                case "tr":
                    fe.Url = "/tr/hakkimizda";
                    break;
                case "en":
                    fe.Url = "/en/about";
                    break;
                case "ru":
                    fe.Url = "/ru/о-компании";
                    break;
                default:
                    fe.Url = "/tr/hakkimizda";
                    break;
            }
        });

        return new GetAboutContentResponse
        {
            Title = contentObj.Title[currentCultureInfo],
            AboutTextPart1 = contentObj.AboutTextPart1[currentCultureInfo],
            AboutTextPart2 = contentObj.AboutTextPart2[currentCultureInfo],
            Languages = language
        };
    }

    public async Task<GetContactInfoResponse> GetContactInfo(string currentCultureInfo)
    {
        var contactInfo = await DbContext.Contents
            .Where(w => w.Key == Constants.CONTACT_INFO)
            .AsNoTracking()
            .FirstOrDefaultAsync();
        
        var contactInfoObj = JsonConvert.DeserializeObject<GetContactInfoResponse>(contactInfo.Value);

        var languages = await GetLanguages(currentCultureInfo);

        languages.ToList().ForEach(fe =>
        {
            switch (fe.Prefix)
            {
                case "tr":
                    fe.Url = "/tr/iletisim";
                    break;
                case "en":
                    fe.Url = "/en/contact";
                    break;
                case "ru":
                    fe.Url = "/ru/cвязь";
                    break;
                default:
                    fe.Url = "tr/iletisim";
                    break;
            }
        });


        contactInfoObj.Languages = languages;
        return contactInfoObj;
    }
    
    //Private Method
    private void AddNavigationLink(StringBuilder output, IEnumerable<NavigationDto> navigations)
    {
        var mainNavigations = navigations.Where(w => w.ParentId == 0).OrderBy(o => o.SortPriority).ToList();

        foreach (var nav in mainNavigations)
        {
            output.Append("<li class=\"menu-item\">");
            if (nav.IsTargetBlank)
                output.Append($"<a class=\"menu-link\" target=\"_blank\"  href=\"{nav.Url}\">");
            else
                output.Append($"<a class=\"menu-link\" href=\"{nav.Url}\">");
            output.Append($"<div>{nav.Caption}</div>");
            output.Append("</a>");

            var subNavigations = navigations.Where(w => w.ParentId == nav.Id).ToList();

            if (subNavigations.Count() > 0) AddSubNavigationLink(output, subNavigations);

            output.Append("</li>");
        }
    }

    private void AddSubNavigationLink(StringBuilder output, IEnumerable<NavigationDto> subNavigations)
    {
        output.Append("<ul class=\"sub-menu-container\">");
        foreach (var nav in subNavigations)
        {
            output.Append("<li class=\"menu-item\">");
            if (nav.IsTargetBlank)
                output.Append($"<a class=\"menu-link\" target=\"_blank\" href=\"{nav.Url}\">");
            else
                output.Append($"<a class=\"menu-link\" href=\"{nav.Url}\">");
            output.Append($"<div>{nav.Caption}</div>");
            output.Append("</a>");

            var childNavigations = subNavigations.Where(w => w.ParentId == nav.Id).ToList();

            if (childNavigations.Any()) AddSubNavigationLink(output, childNavigations);

            output.Append("</li>");
        }

        output.Append("</ul>");
    }
}