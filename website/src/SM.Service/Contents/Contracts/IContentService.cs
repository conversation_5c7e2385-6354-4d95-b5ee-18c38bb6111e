using SM.Service.Contents.DTOs;

namespace SM.Service.Contents.Contracts;

public interface IContentService
{
    Task<IEnumerable<SocialMediaAccountAddressDto>> GetSocialMediaAccountAddresses();

    Task<IEnumerable<LanguageDto>> GetLanguages(string currentCultureInfo);

    Task<IEnumerable<NavigationDto>> GetNavigation(string currentCultureInfo);
    Task<string> GetNavigationHTML(string currentCultureInfo);

    Task<IEnumerable<HomeSliderDto>> GetHomeSlider(string currentCultureInfo);

    Task<GetAboutContentResponse> GetAboutContent(string currentCultureInfo);

    Task<GetContactInfoResponse> GetContactInfo(string currentCultureInfo);
}