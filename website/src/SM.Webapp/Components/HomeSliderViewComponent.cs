using Microsoft.AspNetCore.Mvc;
using SM.Service.Contents.Contracts;
using SM.Webapp.Core;
using SM.Webapp.Models;

namespace SM.WebApp.Components;

public class HomeSliderViewComponent : ViewComponent
{
    private readonly IContentService ContentService;

    public HomeSliderViewComponent(IContentService contentService)
    {
        ContentService = contentService;
    }

    public async Task<IViewComponentResult> InvokeAsync()
    {
        var sliderItems = await ContentService.GetHomeSlider(RouteData.GetLanguage());
        return View(new HomeSliderModel
        {
            SliderItems = sliderItems
        });
    }
}