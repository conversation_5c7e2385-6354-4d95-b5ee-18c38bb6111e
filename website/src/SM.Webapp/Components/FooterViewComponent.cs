using Microsoft.AspNetCore.Mvc;
using SM.Service.Contents.Contracts;
using SM.Webapp.Core;

namespace SM.WebApp.Components;

public class FooterViewComponent : ViewComponent
{
    public FooterViewComponent(IContentService contentService)
    {
        _contentService = contentService;
    }

    private IContentService _contentService { get; }

    public IViewComponentResult Invoke()
    {
        var contactInfo = _contentService.GetContactInfo(RouteData.GetLanguage()).Result;
        return View(contactInfo);
    }
}