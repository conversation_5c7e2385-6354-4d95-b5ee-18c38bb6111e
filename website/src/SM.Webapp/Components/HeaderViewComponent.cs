using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Contents.Contracts;
using SM.Webapp.Core;
using SM.Webapp.Models;

namespace SM.WebApp.Components;

public class HeaderViewComponent : ViewComponent
{
    private readonly IContentService ContentService;

    public HeaderViewComponent(IContentService contentService)
    {
        ContentService = contentService;
    }

    public async Task<IViewComponentResult> InvokeAsync()
    {
        var lang = RouteData.GetLanguage();
        var socialMediaAccountAddresses = ContentService.GetSocialMediaAccountAddresses().Result;
        var languages = ContentService.GetLanguages(lang).Result;
        var navigationHtml = ContentService.GetNavigationHTML(lang).Result;
        
        return View(new HeaderViewModel
        {
            SocialMediaAccountAddresses = socialMediaAccountAddresses,
            Languages = languages,
            NavigationHTML = new HtmlString(navigationHtml)
        });
    }
}