<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <_ContentIncludedByDefault Remove="Views\Home\Index.cshtml"/>
        <_ContentIncludedByDefault Remove="Views\Home\Privacy.cshtml"/>
        <_ContentIncludedByDefault Remove="wwwroot\js\site.js"/>
        <_ContentIncludedByDefault Remove="wwwroot\images\flags\en.png"/>
        <_ContentIncludedByDefault Remove="wwwroot\images\flags\it.png"/>
        <_ContentIncludedByDefault Remove="wwwroot\images\slides\slide-1.jpg"/>
        <_ContentIncludedByDefault Remove="wwwroot\images\slides\slide-2.jpg"/>
        <_ContentIncludedByDefault Remove="wwwroot\images\slides\slide-3.jpg"/>
        <_ContentIncludedByDefault Remove="Views\Shared\Components\Products\Default.cshtml"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="wwwroot\download\brochure\"/>
        <Folder Include="wwwroot\scripts\"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\SM.Localization\SM.Localization.csproj"/>
        <ProjectReference Include="..\SM.Service\SM.Service.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Localization" Version="2.2.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.11">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="NToastNotify" Version="8.0.0"/>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="Resources\Views\Shared\Components\HomeAbout\Default.tr.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>Default.tr.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Resources\Views\Shared\Components\HomeFeatures\Default.tr.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>Default.tr.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Resources\Views\About\Index.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>Index.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Resources\Views\Catalog\Product.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>Product.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Resources\LocalizerRes.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>LocalizerRes.Designer.cs</LastGenOutput>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Resources\Views\Shared\Components\HomeAbout\Default.tr.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>Default.tr.resx</DependentUpon>
        </Compile>
        <Compile Update="Resources\Views\Shared\Components\HomeFeatures\Default.tr.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>Default.tr.resx</DependentUpon>
        </Compile>
        <Compile Update="Resources\Views\About\Index.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>Index.resx</DependentUpon>
        </Compile>
        <Compile Update="Resources\Views\Catalog\Product.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>Product.resx</DependentUpon>
        </Compile>
        <Compile Update="Resources\LocalizerRes.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>LocalizerRes.resx</DependentUpon>
        </Compile>
    </ItemGroup>

</Project>
