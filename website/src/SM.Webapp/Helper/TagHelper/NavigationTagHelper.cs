using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;
using SM.Service.Contents.DTOs;

namespace SM.Webapp.Helper.TagHelper;

[HtmlTargetElement("navigation")]
public class NavigationTagHelper : Microsoft.AspNetCore.Razor.TagHelpers.TagHelper
{
    private readonly HttpContext _httpContext;
    private readonly IUrlHelper _urlHelper;

    public NavigationTagHelper(IHttpContextAccessor accessor, IActionContextAccessor actionContextAccessor,
        IUrlHelperFactory urlHelperFactory)
    {
        _httpContext = accessor.HttpContext;
        _urlHelper = urlHelperFactory.GetUrlHelper(actionContextAccessor.ActionContext);
    }

    [ViewContext] public ViewContext ViewContext { get; set; }

    [HtmlAttributeName("navigation-model")]
    public IEnumerable<NavigationDto> Model { get; set; }

    public override void Process(TagHelperContext context, TagHelperOutput output)
    {
        if (Model == null) return;

        var action = ViewContext.RouteData.Values["action"].ToString();

        output.TagName = "";
        output.Content.AppendHtml("<ul class=\"menu-container justify-content-between\">");
        AddNavigationLink(output, Model);

        output.Content.AppendHtml("</ul>");
    }

    private void AddNavigationLink(TagHelperOutput output, IEnumerable<NavigationDto> navigations)
    {
        var mainNavigations = navigations.Where(w => w.ParentId == 0).OrderBy(o => o.SortPriority).ToList();

        foreach (var nav in mainNavigations)
        {
            output.Content.AppendHtml("<li class=\"menu-item\">");
            output.Content.AppendHtml($"<a class=\"menu-link\" href=\"{nav.Url}\">");
            output.Content.AppendHtml($"<div>{nav.Caption}</div>");
            output.Content.AppendHtml("</a>");

            var subNavigations = navigations.Where(w => w.ParentId == nav.Id).ToList();

            if (subNavigations != null || subNavigations.Count() > 0) AddSubNavigationLink(output, subNavigations);

            output.Content.AppendHtml("</li>");
        }
    }

    private void AddSubNavigationLink(TagHelperOutput output, IEnumerable<NavigationDto> subNavigations)
    {
        foreach (var nav in subNavigations)
        {
            output.Content.AppendHtml("<ul class=\"sub-menu-container\">");
            output.Content.AppendHtml("<li class=\"menu-item\">");
            output.Content.AppendHtml($"<a class=\"menu-link\" href=\"{nav.Url}\">");
            output.Content.AppendHtml($"<div>{nav.Caption}</div>");
            output.Content.AppendHtml("</a>");

            var childNavigations = subNavigations.Where(w => w.ParentId == nav.Id).ToList();

            if (childNavigations.Any()) AddSubNavigationLink(output, childNavigations);

            output.Content.AppendHtml("</li>");
            output.Content.AppendHtml("</ul>");
        }
    }
}