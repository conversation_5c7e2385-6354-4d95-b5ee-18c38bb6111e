/*!
 * Datepicker v0.2.1
 * https://github.com/fengyuanchen/datepicker
 *
 * Copyright (c) 2014-2015 <PERSON><PERSON>
 * Released under the MIT license
 *
 * Date: 2015-10-26T02:21:31.213Z
 */
.datepicker-container {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 210px;
    font-size: 12px;
    line-height: 30px;
    -ms-touch-action: none;
    touch-action: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #fff;
    direction: ltr !important;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

    .datepicker-container:before,
    .datepicker-container:after {
        position: absolute;
        display: block;
        width: 0;
        height: 0;
        content: " ";
        border: 5px solid transparent;
    }

.datepicker-dropdown {
    position: absolute;
    z-index: 9 !important;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    border: 1px solid #ccc;
    -webkit-box-shadow: 0 3px 6px #ccc;
    box-shadow: 0 3px 6px #ccc;
}

.datepicker-inline {
    position: static;
}

.datepicker-top-left,
.datepicker-top-right {
    border-top-color: #6F5499;
}

    .datepicker-top-left:before,
    .datepicker-top-left:after,
    .datepicker-top-right:before,
    .datepicker-top-right:after {
        top: -5px;
        left: 10px;
        border-top: 0;
    }

    .datepicker-top-left:before,
    .datepicker-top-right:before {
        border-bottom-color: #6F5499;
    }

    .datepicker-top-left:after,
    .datepicker-top-right:after {
        top: -4px;
        border-bottom-color: #fff;
    }

.datepicker-bottom-left,
.datepicker-bottom-right {
    border-bottom-color: #6F5499;
}

    .datepicker-bottom-left:before,
    .datepicker-bottom-left:after,
    .datepicker-bottom-right:before,
    .datepicker-bottom-right:after {
        bottom: -5px;
        left: 10px;
        border-bottom: 0;
    }

    .datepicker-bottom-left:before,
    .datepicker-bottom-right:before {
        border-top-color: #6F5499;
    }

    .datepicker-bottom-left:after,
    .datepicker-bottom-right:after {
        bottom: -4px;
        border-top-color: #fff;
    }

    .datepicker-top-right:before,
    .datepicker-top-right:after,
    .datepicker-bottom-right:before,
    .datepicker-bottom-right:after {
        right: 10px;
        left: auto;
    }

.datepicker-panel > ul:before,
.datepicker-panel > ul:after {
    display: table;
    content: " ";
}

.datepicker-panel > ul:after {
    clear: both;
}

.datepicker-panel > ul {
    width: 102%;
    padding: 0;
    margin: 0;
}

    .datepicker-panel > ul > li {
        float: left;
        width: 30px;
        height: 30px;
        padding: 0;
        margin: 0;
        text-align: center;
        list-style: none;
        cursor: pointer;
        background-color: #fff;
    }

        .datepicker-panel > ul > li:hover {
            background-color: #eee;
        }

        .datepicker-panel > ul > li.muted,
        .datepicker-panel > ul > li.muted:hover {
            color: #999;
        }

        .datepicker-panel > ul > li.picked,
        .datepicker-panel > ul > li.picked:hover {
            color: #6F5499;
        }

        .datepicker-panel > ul > li.disabled,
        .datepicker-panel > ul > li.disabled:hover {
            color: #ccc;
            cursor: default;
            background-color: #fff;
        }

        .datepicker-panel > ul > li[data-view="years prev"],
        .datepicker-panel > ul > li[data-view="year prev"],
        .datepicker-panel > ul > li[data-view="month prev"],
        .datepicker-panel > ul > li[data-view="years next"],
        .datepicker-panel > ul > li[data-view="year next"],
        .datepicker-panel > ul > li[data-view="month next"],
        .datepicker-panel > ul > li[data-view="next"] {
            font-size: 18px;
        }

        .datepicker-panel > ul > li[data-view="years current"],
        .datepicker-panel > ul > li[data-view="year current"],
        .datepicker-panel > ul > li[data-view="month current"] {
            width: 150px;
        }

    .datepicker-panel > ul[data-view="years"] > li,
    .datepicker-panel > ul[data-view="months"] > li {
        width: 52.5px;
        height: 52.5px;
        line-height: 52.5px;
    }

    .datepicker-panel > ul[data-view="week"] > li,
    .datepicker-panel > ul[data-view="week"] > li:hover {
        cursor: default;
        background-color: #fff;
    }

.datepicker-hide {
    display: none;
}
/*
==============================
CONTACT FORM
==============================
*/
.form-ajax .success-box, .form-ajax .error-box {
    display: none;
    margin-top: 20px;
}

.form-ajax-wp.label-visible {
    margin-top: -10px;
}

.form-box [class*="col-md-"] {
    padding: 0 6.5px;
}

.form-box > .row {
    margin-left: -6.5px;
    margin-right: -6.5px;
}

.form-box > .space.xs {
    height: 12px;
}

.form-box.text-center input, .form-box.text-center textarea, .form-box.text-center select {
    text-align: center;
}
 .form-inline {
       display: flex;
    flex: 0 0 100%;
}
.form-inline  > .row {
       display: flex;
       flex: 0 0 100%;
       flex: 0 0 calc(100% + 13px);
}
    .form-inline hr {
        display: none;
    }

    .form-inline input, .form-inline .btn {
        height: 35px;
    }

    .form-inline .btn {
        line-height: 18px;
        margin-left: 15px;
        white-space: nowrap;
        max-width: calc(100% - 15px);
    }

        .form-inline .btn i {
            display: inline-block;
            transform: translateY(2px);
            font-size: 13px;
        }

.form-box:not(.form-inline) .g-recaptcha {
    margin-bottom: 15px;
}

.form-box .cf-loader {
    height: 25px;
    margin-left: 10px;
    display: none;
}

/*
==============================
MOBILE - RESPONSIVE
==============================
*/
@media (max-width: 992px) {
    .form-box > .row {
        margin-left: -15px;
        margin-right: -15px;
            display: block;
    }

    .form-box [class*="col-md-"] {
        padding: 0 15px;
    }

    .form-box .space {
        display: none;
    }

    .form-box input, .form-box textarea, .form-box select {
        margin-bottom: 15px;
    }

    .form-box [class*="col-md"] {
        padding-bottom: 0;
        padding-top: 0;
    }

    .form-inline {
        display: block;
    }

        .form-inline .btn {
            float: none !important;
            max-width:100%;
            margin-left: 0;
        }
}
