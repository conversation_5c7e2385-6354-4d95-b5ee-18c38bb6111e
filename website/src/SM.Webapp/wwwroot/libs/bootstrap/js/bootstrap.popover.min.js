+function (n) { "use strict"; function r(i) { return this.each(function () { var u = n(this), r = u.data("bs.tooltip"), f = typeof i == "object" && i; (r || !/destroy|hide/.test(i)) && (r || u.data("bs.tooltip", r = new t(this, f)), typeof i == "string" && r[i]()) }) } var t = function (n, t) { this.type = null; this.options = null; this.enabled = null; this.timeout = null; this.hoverState = null; this.$element = null; this.inState = null; this.init("tooltip", n, t) }, i; t.VERSION = "3.3.5"; t.TRANSITION_DURATION = 150; t.DEFAULTS = { animation: !0, placement: "top", selector: !1, template: '<div class="tooltip" role="tooltip"><div class="tooltip-arrow"><\/div><div class="tooltip-inner"><\/div><\/div>', trigger: "hover focus", title: "", delay: 0, html: !1, container: !1, viewport: { selector: "body", padding: 0 } }; t.prototype.init = function (t, i, r) { var f, e, u, o, s; if (this.enabled = !0, this.type = t, this.$element = n(i), this.options = this.getOptions(r), this.$viewport = this.options.viewport && n(n.isFunction(this.options.viewport) ? this.options.viewport.call(this, this.$element) : this.options.viewport.selector || this.options.viewport), this.inState = { click: !1, hover: !1, focus: !1 }, this.$element[0] instanceof document.constructor && !this.options.selector) throw new Error("`selector` option must be specified when initializing " + this.type + " on the window.document object!"); for (f = this.options.trigger.split(" "), e = f.length; e--;) if (u = f[e], u == "click") this.$element.on("click." + this.type, this.options.selector, n.proxy(this.toggle, this)); else if (u != "manual") { o = u == "hover" ? "mouseenter" : "focusin"; s = u == "hover" ? "mouseleave" : "focusout"; this.$element.on(o + "." + this.type, this.options.selector, n.proxy(this.enter, this)); this.$element.on(s + "." + this.type, this.options.selector, n.proxy(this.leave, this)) } this.options.selector ? this._options = n.extend({}, this.options, { trigger: "manual", selector: "" }) : this.fixTitle() }; t.prototype.getDefaults = function () { return t.DEFAULTS }; t.prototype.getOptions = function (t) { return t = n.extend({}, this.getDefaults(), this.$element.data(), t), t.delay && typeof t.delay == "number" && (t.delay = { show: t.delay, hide: t.delay }), t }; t.prototype.getDelegateOptions = function () { var t = {}, i = this.getDefaults(); return this._options && n.each(this._options, function (n, r) { i[n] != r && (t[n] = r) }), t }; t.prototype.enter = function (t) { var i = t instanceof this.constructor ? t : n(t.currentTarget).data("bs." + this.type); if (i || (i = new this.constructor(t.currentTarget, this.getDelegateOptions()), n(t.currentTarget).data("bs." + this.type, i)), t instanceof n.Event && (i.inState[t.type == "focusin" ? "focus" : "hover"] = !0), i.tip().hasClass("in") || i.hoverState == "in") { i.hoverState = "in"; return } if (clearTimeout(i.timeout), i.hoverState = "in", !i.options.delay || !i.options.delay.show) return i.show(); i.timeout = setTimeout(function () { i.hoverState == "in" && i.show() }, i.options.delay.show) }; t.prototype.isInStateTrue = function () { for (var n in this.inState) if (this.inState[n]) return !0; return !1 }; t.prototype.leave = function (t) { var i = t instanceof this.constructor ? t : n(t.currentTarget).data("bs." + this.type); if (i || (i = new this.constructor(t.currentTarget, this.getDelegateOptions()), n(t.currentTarget).data("bs." + this.type, i)), t instanceof n.Event && (i.inState[t.type == "focusout" ? "focus" : "hover"] = !1), !i.isInStateTrue()) { if (clearTimeout(i.timeout), i.hoverState = "out", !i.options.delay || !i.options.delay.hide) return i.hide(); i.timeout = setTimeout(function () { i.hoverState == "out" && i.hide() }, i.options.delay.hide) } }; t.prototype.show = function () { var c = n.Event("show.bs." + this.type), l, p, e, w, h; if (this.hasContent() && this.enabled) { if (this.$element.trigger(c), l = n.contains(this.$element[0].ownerDocument.documentElement, this.$element[0]), c.isDefaultPrevented() || !l) return; var u = this, r = this.tip(), a = this.getUID(this.type); this.setContent(); r.attr("id", a); this.$element.attr("aria-describedby", a); this.options.animation && r.addClass("fade"); var i = typeof this.options.placement == "function" ? this.options.placement.call(this, r[0], this.$element[0]) : this.options.placement, v = /\s?auto?\s?/i, y = v.test(i); y && (i = i.replace(v, "") || "top"); r.detach().css({ top: 0, left: 0, display: "block" }).addClass(i).data("bs." + this.type, this); this.options.container ? r.appendTo(this.options.container) : r.insertAfter(this.$element); this.$element.trigger("inserted.bs." + this.type); var f = this.getPosition(), o = r[0].offsetWidth, s = r[0].offsetHeight; y && (p = i, e = this.getPosition(this.$viewport), i = i == "bottom" && f.bottom + s > e.bottom ? "top" : i == "top" && f.top - s < e.top ? "bottom" : i == "right" && f.right + o > e.width ? "left" : i == "left" && f.left - o < e.left ? "right" : i, r.removeClass(p).addClass(i)); w = this.getCalculatedOffset(i, f, o, s); this.applyPlacement(w, i); h = function () { var n = u.hoverState; u.$element.trigger("shown.bs." + u.type); u.hoverState = null; n == "out" && u.leave(u) }; n.support.transition && this.$tip.hasClass("fade") ? r.one("bsTransitionEnd", h).emulateTransitionEnd(t.TRANSITION_DURATION) : h() } }; t.prototype.applyPlacement = function (t, i) { var r = this.tip(), l = r[0].offsetWidth, e = r[0].offsetHeight, o = parseInt(r.css("margin-top"), 10), s = parseInt(r.css("margin-left"), 10), h, f, u; isNaN(o) && (o = 0); isNaN(s) && (s = 0); t.top += o; t.left += s; n.offset.setOffset(r[0], n.extend({ using: function (n) { r.css({ top: Math.round(n.top), left: Math.round(n.left) }) } }, t), 0); r.addClass("in"); h = r[0].offsetWidth; f = r[0].offsetHeight; i == "top" && f != e && (t.top = t.top + e - f); u = this.getViewportAdjustedDelta(i, t, h, f); u.left ? t.left += u.left : t.top += u.top; var c = /top|bottom/.test(i), a = c ? u.left * 2 - l + h : u.top * 2 - e + f, v = c ? "offsetWidth" : "offsetHeight"; r.offset(t); this.replaceArrow(a, r[0][v], c) }; t.prototype.replaceArrow = function (n, t, i) { this.arrow().css(i ? "left" : "top", 50 * (1 - n / t) + "%").css(i ? "top" : "left", "") }; t.prototype.setContent = function () { var n = this.tip(), t = this.getTitle(); n.find(".tooltip-inner")[this.options.html ? "html" : "text"](t); n.removeClass("fade in top bottom left right") }; t.prototype.hide = function (i) { function e() { u.hoverState != "in" && r.detach(); u.$element.removeAttr("aria-describedby").trigger("hidden.bs." + u.type); i && i() } var u = this, r = n(this.$tip), f = n.Event("hide.bs." + this.type); if (this.$element.trigger(f), !f.isDefaultPrevented()) return r.removeClass("in"), n.support.transition && r.hasClass("fade") ? r.one("bsTransitionEnd", e).emulateTransitionEnd(t.TRANSITION_DURATION) : e(), this.hoverState = null, this }; t.prototype.fixTitle = function () { var n = this.$element; (n.attr("title") || typeof n.attr("data-original-title") != "string") && n.attr("data-original-title", n.attr("title") || "").attr("title", "") }; t.prototype.hasContent = function () { return this.getTitle() }; t.prototype.getPosition = function (t) { t = t || this.$element; var u = t[0], r = u.tagName == "BODY", i = u.getBoundingClientRect(); i.width == null && (i = n.extend({}, i, { width: i.right - i.left, height: i.bottom - i.top })); var f = r ? { top: 0, left: 0 } : t.offset(), e = { scroll: r ? document.documentElement.scrollTop || document.body.scrollTop : t.scrollTop() }, o = r ? { width: n(window).width(), height: n(window).height() } : null; return n.extend({}, i, e, o, f) }; t.prototype.getCalculatedOffset = function (n, t, i, r) { return n == "bottom" ? { top: t.top + t.height, left: t.left + t.width / 2 - i / 2 } : n == "top" ? { top: t.top - r, left: t.left + t.width / 2 - i / 2 } : n == "left" ? { top: t.top + t.height / 2 - r / 2, left: t.left - i } : { top: t.top + t.height / 2 - r / 2, left: t.left + t.width } }; t.prototype.getViewportAdjustedDelta = function (n, t, i, r) { var f = { top: 0, left: 0 }, e, u, o, s, h, c; return this.$viewport ? (e = this.options.viewport && this.options.viewport.padding || 0, u = this.getPosition(this.$viewport), /right|left/.test(n) ? (o = t.top - e - u.scroll, s = t.top + e - u.scroll + r, o < u.top ? f.top = u.top - o : s > u.top + u.height && (f.top = u.top + u.height - s)) : (h = t.left - e, c = t.left + e + i, h < u.left ? f.left = u.left - h : c > u.right && (f.left = u.left + u.width - c)), f) : f }; t.prototype.getTitle = function () { var t = this.$element, n = this.options; return t.attr("data-original-title") || (typeof n.title == "function" ? n.title.call(t[0]) : n.title) }; t.prototype.getUID = function (n) { do n += ~~(Math.random() * 1e6); while (document.getElementById(n)); return n }; t.prototype.tip = function () { if (!this.$tip && (this.$tip = n(this.options.template), this.$tip.length != 1)) throw new Error(this.type + " `template` option must consist of exactly 1 top-level element!"); return this.$tip }; t.prototype.arrow = function () { return this.$arrow = this.$arrow || this.tip().find(".tooltip-arrow") }; t.prototype.enable = function () { this.enabled = !0 }; t.prototype.disable = function () { this.enabled = !1 }; t.prototype.toggleEnabled = function () { this.enabled = !this.enabled }; t.prototype.toggle = function (t) { var i = this; t && (i = n(t.currentTarget).data("bs." + this.type), i || (i = new this.constructor(t.currentTarget, this.getDelegateOptions()), n(t.currentTarget).data("bs." + this.type, i))); t ? (i.inState.click = !i.inState.click, i.isInStateTrue() ? i.enter(i) : i.leave(i)) : i.tip().hasClass("in") ? i.leave(i) : i.enter(i) }; t.prototype.destroy = function () { var n = this; clearTimeout(this.timeout); this.hide(function () { n.$element.off("." + n.type).removeData("bs." + n.type); n.$tip && n.$tip.detach(); n.$tip = null; n.$arrow = null; n.$viewport = null }) }; i = n.fn.tooltip; n.fn.tooltip = r; n.fn.tooltip.Constructor = t; n.fn.tooltip.noConflict = function () { return n.fn.tooltip = i, this } }(jQuery); +function (n) { "use strict"; function r(i) { return this.each(function () { var u = n(this), r = u.data("bs.popover"), f = typeof i == "object" && i; (r || !/destroy|hide/.test(i)) && (r || u.data("bs.popover", r = new t(this, f)), typeof i == "string" && r[i]()) }) } var t = function (n, t) { this.init("popover", n, t) }, i; if (!n.fn.tooltip) throw new Error("Popover requires tooltip.js"); t.VERSION = "3.3.5"; t.DEFAULTS = n.extend({}, n.fn.tooltip.Constructor.DEFAULTS, { placement: "right", trigger: "click", content: "", template: '<div class="popover" role="tooltip"><div class="arrow"><\/div><h3 class="popover-title"><\/h3><div class="popover-content"><\/div><\/div>' }); t.prototype = n.extend({}, n.fn.tooltip.Constructor.prototype); t.prototype.constructor = t; t.prototype.getDefaults = function () { return t.DEFAULTS }; t.prototype.setContent = function () { var n = this.tip(), i = this.getTitle(), t = this.getContent(); n.find(".popover-title")[this.options.html ? "html" : "text"](i); n.find(".popover-content").children().detach().end()[this.options.html ? typeof t == "string" ? "html" : "append" : "text"](t); n.removeClass("fade top bottom left right in"); n.find(".popover-title").html() || n.find(".popover-title").hide() }; t.prototype.hasContent = function () { return this.getTitle() || this.getContent() }; t.prototype.getContent = function () { var t = this.$element, n = this.options; return t.attr("data-content") || (typeof n.content == "function" ? n.content.call(t[0]) : n.content) }; t.prototype.arrow = function () { return this.$arrow = this.$arrow || this.tip().find(".arrow") }; i = n.fn.popover; n.fn.popover = r; n.fn.popover.Constructor = t; n.fn.popover.noConflict = function () { return n.fn.popover = i, this } }(jQuery);

/*
* Pixor - Copyright (c) Federico Schiocchet - Pixor (www.pixor.it) - Framework Y (www.framework-y.com)
*/

(function ($) {
    $('[data-toggle="tooltip"]').tooltip();
    $('[data-toggle="popover"]').popover();
}(jQuery));
 