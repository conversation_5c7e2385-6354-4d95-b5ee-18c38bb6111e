@font-face {
    font-family: 'icomoon';
    src: url('line-icons-fonts/icomoon.eot');
    src: url('line-icons-fonts/icomoon.eot?#iefix-rdmvgc') format('embedded-opentype'), url('line-icons-fonts/icomoon.woff') format('woff'), url('line-icons-fonts/icomoon.ttf') format('truetype'), url('line-icons-fonts/icomoon.svg?-rdmvgc#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
}

[class^="im-"], [class*=" im-"], .maso-order i, .fa-search, .mfp-preloader, .mfp-arrow:before, .fa-bookmark, .fa-pencil, .album-title .btn .fa, .bootgrid-table thead .fa, .side-menu .fa.arrow,
.fa-share-alt, .pagination .fa-angle-left, .pagination .fa-angle-right, .porfolio-bar i, .fa-calendar, .fa-angle-up.scroll-top, .adv-img-button-content .caption i, .popup-close, .side-menu .plus-times,
.dropdown-submenu > a:after, .navbar-toggle .fa-bars, header .fa-bars, .hamburger-button i:before, .fa-angle-double-left, .fa-angle-double-right, .fa-angle-down, .fa-comment-o, .scroll-top-mobile:before,
.advs-box .btn .fa-long-arrow-right,.adv-img .btn .fa-long-arrow-right:before,.box-steps .step-item:after,.fa-shopping-cart:before {
    font-family: 'icomoon' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-transform: none;
}

/*
Compatibility icons
Automatic conversion from Font Awesome to Icons Mind
*/
.scroll-top-mobile:before {
    content: "\ea29";
    transform: rotate(90deg);
    font-size: 20px;
    display: inline-block;
}

.flex-direction-nav a:before {
    font-family: "icomoon" !important;
    font-size: 50px !important;
    content: '\ea29' !important;
}

.flex-direction-nav a.flex-next:before {
    content: '\ebfc' !important;
}

.block-quote.quote-1:before, .block-quote.quote-2:before {
    content: "\201c";
    font-size: 140px;
    line-height: 57px;
}

.block-quote.quote-2:after {
    content: "\201d";
    font-size: 140px;
    line-height: 95px;
}

.mfp-arrow-right {
    right: 18px;
}
@media (max-width: 992px) {
  .mfp-arrow:before, .mfp-arrow:after, .mfp-arrow .mfp-b, .mfp-arrow .mfp-a {
        margin: 12px 18px;
    }
}
.album-title .btn .fa:before {
    content: "\ea29";
}

.bootgrid-table thead .fa-angle-down:before {
    content: "\ed84";
    font-size: 24px;
}

.bootgrid-table thead .fa-angle-up:before {
    content: "\ed8c";
    font-size: 24px;
}

.bootgrid-header .search .fa {
    font-weight: 900;
}

.pagination .fa {
    font-size: 15px;
    display: inline-block;
    transform: translateY(2px);
    font-weight: 900;
}

.fa-angle-up.scroll-top {
    font-size: 12px;
    line-height: 19px;
    font-weight: 900;
}

.adv-img-down-text .fa-plus:before {
    content: "+";
}

.load-more-maso i, .load-more-grid i {
    display: none;
}

.fullpage-varrow.varrow-circle .arrow i {
    font-size: 15px;
    font-weight: 600;
}

.maso-order .fa-arrow-down:before, .maso-order .fa-arrow-up:before {
    content: '\5E';
    transform: rotate(180deg);
    display: inline-block;
    font-size: 16px;
    padding-bottom: 2px;
    margin-left: 0;
}

.maso-order .fa-arrow-up:before {
    transform: rotate(0) translateY(4px);
    padding-bottom: 0;
}
.box-steps .step-item:after {
    content: '\e660';
}
.im-play:before {
    content: "\25BA";
}


/*
Font Awesome
Social icons and compatibility styles
*/
@font-face {
    font-family: "fontawesome";
    src: url("line-icons-fonts/social.eot");
    src: url("line-icons-fonts/social.eot?#iefix") format("embedded-opentype"), url("line-icons-fonts/social.woff") format("woff"), url("line-icons-fonts/social.ttf") format("truetype"), url("line-icons-fonts/social.svg#social") format("svg");
    font-weight: normal;
    font-style: normal;
}

.fa {
    display: inline-block;
    font: normal normal normal 14px/1 fontawesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transform: translate(0, 0);
    padding-left: 1px;
    text-transform: none;
}

.fa-ul {
    padding-left: 0;
    margin-left: 46px;
    list-style-type: none;
}

    .fa-ul > li {
        position: relative;
    }

.fa-li {
    position: absolute;
    left: -2.08em;
    width: 2.14285714em;
    top: -5px;
    text-align: center;
    font-size: 20px;
}

.maso-order i {
    font-weight: 600;
}


.fa-paypal:before {
    content: "\61";
}

.fa-facebook-official:before {
    content: "\62";
}

.fa-facebook:before {
    content: "\63";
}

.fa-twitter:before {
    content: "\64";
}

.fa-twitch:before {
    content: "\65";
}

.fa-twitter-square:before {
    content: "\66";
}

.fa-youtube-square:before {
    content: "\67";
}

.fa-youtube:before {
    content: "\69";
}

.fa-google-plus:before, .fa-google:before {
    content: "\68";
}

.fa-google-plus-square:before {
    content: "\6a";
}

.fa-linkedin:before {
    content: "\6b";
}

.fa-linkedin-square:before {
    content: "\6c";
}

.fa-instagram:before {
    content: "\6d";
}

.fa-pinterest:before {
    content: "\6e";
}

.fa-pinterest-p:before {
    content: "\6f";
}

.fa-pinterest-square:before {
    content: "\70";
}

.fa-skype:before {
    content: "\71";
}

.fa-paypal-1:before {
    content: "\72";
}

.fa-soundcloud:before {
    content: "\73";
}

.fa-spotify:before {
    content: "\74";
}

.fa-stack-overflow:before {
    content: "\75";
}

.fa-tripadvisor:before {
    content: "\76";
}

.fa-wordpress:before {
    content: "\77";
}

.fa-windows:before {
    content: "\78";
}

.fa-vimeo-square:before {
    content: "\79";
}

.fa-vimeo:before {
    content: "\7a";
}

.fa-apple:before {
    content: "\41";
}

.fa-android:before {
    content: "\42";
}

.fa-amazon:before {
    content: "\43";
}

.fa-500px:before {
    content: "\44";
}


/*
Icons Mind
*/

.im-a-z:before {
    content: "\e600";
}

.im-aa:before {
    content: "\e601";
}

.im-add-bag:before {
    content: "\e602";
}

.im-add-basket:before {
    content: "\e603";
}

.im-add-cart:before {
    content: "\e604";
}

.im-add-file:before {
    content: "\e605";
}

.im-add-spaceafterparagraph:before {
    content: "\e606";
}

.im-add-spacebeforeparagraph:before {
    content: "\e607";
}

.im-add-user:before {
    content: "\e608";
}

.im-add-userstar:before {
    content: "\e609";
}

.im-add-window:before {
    content: "\e60a";
}

.im-add:before {
    content: "\e60b";
}

.im-address-book:before {
    content: "\e60c";
}

.im-address-book2:before {
    content: "\e60d";
}

.im-administrator:before {
    content: "\e60e";
}

.im-aerobics-2:before {
    content: "\e60f";
}

.im-aerobics-3:before {
    content: "\e610";
}

.im-aerobics:before {
    content: "\e611";
}

.im-affiliate:before {
    content: "\e612";
}

.im-aim:before {
    content: "\e613";
}

.im-air-balloon:before {
    content: "\e614";
}

.im-airbrush:before {
    content: "\e615";
}

.im-airship:before {
    content: "\e616";
}

.im-alarm-clock:before {
    content: "\e617";
}

.im-alarm-clock2:before {
    content: "\e618";
}

.im-alarm:before {
    content: "\e619";
}

.im-alien-2:before {
    content: "\e61a";
}

.im-alien:before {
    content: "\e61b";
}

.im-aligator:before {
    content: "\e61c";
}

.im-align-center:before {
    content: "\e61d";
}

.im-align-justifyall:before, .porfolio-bar .fa-th:before, .navbar-toggle .fa-bars:before, .hamburger-button i:before, header .fa-bars:before {
    content: "\e61e";
}

.im-align-justifycenter:before {
    content: "\e61f";
}

.im-align-justifyleft:before {
    content: "\e620";
}

.im-align-justifyright:before {
    content: "\e621";
}

.im-align-left:before {
    content: "\e622";
}

.im-align-right:before {
    content: "\e623";
}

.im-alpha:before {
    content: "\e624";
}

.im-ambulance:before {
    content: "\e625";
}

.im-amx:before {
    content: "\e626";
}

.im-anchor-2:before {
    content: "\e627";
}

.im-anchor:before {
    content: "\e628";
}

.im-android-store:before {
    content: "\e629";
}

.im-android:before {
    content: "\e62a";
}

.im-angel-smiley:before {
    content: "\e62b";
}

.im-angel:before {
    content: "\e62c";
}

.im-angry:before {
    content: "\e62d";
}

.im-apple-bite:before {
    content: "\e62e";
}

.im-apple-store:before {
    content: "\e62f";
}

.im-apple:before {
    content: "\e630";
}

.im-approved-window:before {
    content: "\e631";
}

.im-aquarius-2:before {
    content: "\e632";
}

.im-aquarius:before {
    content: "\e633";
}

.im-archery-2:before {
    content: "\e634";
}

.im-archery:before {
    content: "\e635";
}

.im-argentina:before {
    content: "\e636";
}

.im-aries-2:before {
    content: "\e637";
}

.im-aries:before {
    content: "\e638";
}

.im-army-key:before {
    content: "\e639";
}

.im-arrow-around:before {
    content: "\e63a";
}

.im-arrow-back3:before {
    content: "\e63b";
}

.im-arrow-back:before {
    content: "\e63c";
}

.im-arrow-back2:before {
    content: "\e63d";
}

.im-arrow-barrier:before {
    content: "\e63e";
}

.im-arrow-circle:before {
    content: "\e63f";
}

.im-arrow-cross:before {
    content: "\e640";
}

.im-arrow-down:before, .fa-angle-down:before {
    content: "\e641";
}

.im-arrow-down2:before {
    content: "\e642";
}

.im-arrow-down3:before {
    content: "\e643";
}

.im-arrow-downincircle:before {
    content: "\e644";
}

.im-arrow-fork:before {
    content: "\e645";
}

.im-arrow-forward:before {
    content: "\e646";
}

.im-arrow-forward2:before {
    content: "\e647";
}

.im-arrow-from:before {
    content: "\e648";
}

.im-arrow-inside:before {
    content: "\e649";
}

.im-arrow-inside45:before {
    content: "\e64a";
}

.im-arrow-insidegap:before {
    content: "\e64b";
}

.im-arrow-insidegap45:before {
    content: "\e64c";
}

.im-arrow-into:before {
    content: "\e64d";
}

.im-arrow-join:before {
    content: "\e64e";
}

.im-arrow-junction:before {
    content: "\e64f";
}

.im-arrow-left:before {
    content: "\e650";
}

.im-arrow-left2:before {
    content: "\e651";
}

.im-arrow-leftincircle:before {
    content: "\e652";
}

.im-arrow-loop:before {
    content: "\e653";
}

.im-arrow-merge:before {
    content: "\e654";
}

.im-arrow-mix:before {
    content: "\e655";
}

.im-arrow-next:before {
    content: "\e656";
}

.im-arrow-outleft:before {
    content: "\e657";
}

.im-arrow-outright:before {
    content: "\e658";
}

.im-arrow-outside:before {
    content: "\e659";
}

.im-arrow-outside45:before {
    content: "\e65a";
}

.im-arrow-outsidegap:before {
    content: "\e65b";
}

.im-arrow-outsidegap45:before {
    content: "\e65c";
}

.im-arrow-over:before {
    content: "\e65d";
}

.im-arrow-refresh:before {
    content: "\e65e";
}

.im-arrow-refresh2:before {
    content: "\e65f";
}

.im-arrow-right:before {
    content: "\e660";
}

.im-arrow-right2:before {
    content: "\e661";
}

.im-arrow-rightincircle:before {
    content: "\e662";
}

.im-arrow-shuffle:before {
    content: "\e663";
}

.im-arrow-squiggly:before {
    content: "\e664";
}

.im-arrow-through:before {
    content: "\e665";
}

.im-arrow-to:before {
    content: "\e666";
}

.im-arrow-turnleft:before {
    content: "\e667";
}

.im-arrow-turnright:before {
    content: "\e668";
}

.im-arrow-up:before, .fa-angle-up.scroll-top:before {
    content: "\e669";
}

.im-arrow-up2:before {
    content: "\e66a";
}

.im-arrow-up3:before {
    content: "\e66b";
}

.im-arrow-upincircle:before {
    content: "\e66c";
}

.im-arrow-xleft:before {
    content: "\e66d";
}

.im-arrow-xright:before {
    content: "\e66e";
}

.im-ask:before {
    content: "\e66f";
}

.im-assistant:before {
    content: "\e670";
}

.im-astronaut:before {
    content: "\e671";
}

.im-at-sign:before {
    content: "\e672";
}

.im-atm:before {
    content: "\e673";
}

.im-atom:before {
    content: "\e674";
}

.im-audio:before {
    content: "\e675";
}

.im-auto-flash:before {
    content: "\e676";
}

.im-autumn:before {
    content: "\e677";
}

.im-baby-clothes:before {
    content: "\e678";
}

.im-baby-clothes2:before {
    content: "\e679";
}

.im-baby-cry:before {
    content: "\e67a";
}

.im-baby:before {
    content: "\e67b";
}

.im-back2:before {
    content: "\e67c";
}

.im-back-media:before {
    content: "\e67d";
}

.im-back-music:before {
    content: "\e67e";
}

.im-back:before {
    content: "\e67f";
}

.im-background:before {
    content: "\e680";
}

.im-bacteria:before {
    content: "\e681";
}

.im-bag-coins:before {
    content: "\e682";
}

.im-bag-items:before {
    content: "\e683";
}

.im-bag-quantity:before {
    content: "\e684";
}

.im-bag:before {
    content: "\e685";
}

.im-bakelite:before {
    content: "\e686";
}

.im-ballet-shoes:before {
    content: "\e687";
}

.im-balloon:before {
    content: "\e688";
}

.im-banana:before {
    content: "\e689";
}

.im-band-aid:before {
    content: "\e68a";
}

.im-bank:before {
    content: "\e68b";
}

.im-bar-chart:before {
    content: "\e68c";
}

.im-bar-chart2:before {
    content: "\e68d";
}

.im-bar-chart3:before {
    content: "\e68e";
}

.im-bar-chart4:before {
    content: "\e68f";
}

.im-bar-chart5:before {
    content: "\e690";
}

.im-bar-code:before {
    content: "\e691";
}

.im-barricade-2:before {
    content: "\e692";
}

.im-barricade:before {
    content: "\e693";
}

.im-baseball:before {
    content: "\e694";
}

.im-basket-ball:before {
    content: "\e695";
}

.im-basket-coins:before {
    content: "\e696";
}

.im-basket-items:before {
    content: "\e697";
}

.im-basket-quantity:before {
    content: "\e698";
}

.im-bat-2:before {
    content: "\e699";
}

.im-bat:before {
    content: "\e69a";
}

.im-bathrobe:before {
    content: "\e69b";
}

.im-batman-mask:before {
    content: "\e69c";
}

.im-battery-0:before {
    content: "\e69d";
}

.im-battery-25:before {
    content: "\e69e";
}

.im-battery-50:before {
    content: "\e69f";
}

.im-battery-75:before {
    content: "\e6a0";
}

.im-battery-100:before {
    content: "\e6a1";
}

.im-battery-charge:before {
    content: "\e6a2";
}

.im-bear:before {
    content: "\e6a3";
}

.im-beard-2:before {
    content: "\e6a4";
}

.im-beard-3:before {
    content: "\e6a5";
}

.im-beard:before {
    content: "\e6a6";
}

.im-bebo:before {
    content: "\e6a7";
}

.im-bee:before {
    content: "\e6a8";
}

.im-beer-glass:before {
    content: "\e6a9";
}

.im-beer:before {
    content: "\e6aa";
}

.im-bell-2:before {
    content: "\e6ab";
}

.im-bell:before {
    content: "\e6ac";
}

.im-belt-2:before {
    content: "\e6ad";
}

.im-belt-3:before {
    content: "\e6ae";
}

.im-belt:before {
    content: "\e6af";
}

.im-berlin-tower:before {
    content: "\e6b0";
}

.im-beta:before {
    content: "\e6b1";
}

.im-betvibes:before {
    content: "\e6b2";
}

.im-bicycle-2:before {
    content: "\e6b3";
}

.im-bicycle-3:before {
    content: "\e6b4";
}

.im-bicycle:before {
    content: "\e6b5";
}

.im-big-bang:before {
    content: "\e6b6";
}

.im-big-data:before {
    content: "\e6b7";
}

.im-bike-helmet:before {
    content: "\e6b8";
}

.im-bikini:before {
    content: "\e6b9";
}

.im-bilk-bottle2:before {
    content: "\e6ba";
}

.im-billing:before {
    content: "\e6bb";
}

.im-bing:before {
    content: "\e6bc";
}

.im-binocular:before {
    content: "\e6bd";
}

.im-bio-hazard:before {
    content: "\e6be";
}

.im-biotech:before {
    content: "\e6bf";
}

.im-bird-deliveringletter:before {
    content: "\e6c0";
}

.im-bird:before {
    content: "\e6c1";
}

.im-birthday-cake:before {
    content: "\e6c2";
}

.im-bisexual:before {
    content: "\e6c3";
}

.im-bishop:before {
    content: "\e6c4";
}

.im-bitcoin:before {
    content: "\e6c5";
}

.im-black-cat:before {
    content: "\e6c6";
}

.im-blackboard:before {
    content: "\e6c7";
}

.im-blinklist:before {
    content: "\e6c8";
}

.im-block-cloud:before {
    content: "\e6c9";
}

.im-block-window:before {
    content: "\e6ca";
}

.im-blogger:before {
    content: "\e6cb";
}

.im-blood:before {
    content: "\e6cc";
}

.im-blouse:before {
    content: "\e6cd";
}

.im-blueprint:before {
    content: "\e6ce";
}

.im-board:before {
    content: "\e6cf";
}

.im-bodybuilding:before {
    content: "\e6d0";
}

.im-bold-text:before {
    content: "\e6d1";
}

.im-bone:before {
    content: "\e6d2";
}

.im-bones:before {
    content: "\e6d3";
}

.im-book:before {
    content: "\e6d4";
}

.im-bookmark:before {
    content: "\e6d5";
}

.im-books-2:before {
    content: "\e6d6";
}

.im-books:before {
    content: "\e6d7";
}

.im-boom:before {
    content: "\e6d8";
}

.im-boot-2:before {
    content: "\e6d9";
}

.im-boot:before {
    content: "\e6da";
}

.im-bottom-totop:before {
    content: "\e6db";
}

.im-bow-2:before {
    content: "\e6dc";
}

.im-bow-3:before {
    content: "\e6dd";
}

.im-bow-4:before {
    content: "\e6de";
}

.im-bow-5:before {
    content: "\e6df";
}

.im-bow-6:before {
    content: "\e6e0";
}

.im-bow:before {
    content: "\e6e1";
}

.im-bowling-2:before {
    content: "\e6e2";
}

.im-bowling:before {
    content: "\e6e3";
}

.im-box2:before {
    content: "\e6e4";
}

.im-box-close:before {
    content: "\e6e5";
}

.im-box-full:before {
    content: "\e6e6";
}

.im-box-open:before {
    content: "\e6e7";
}

.im-box-withfolders:before {
    content: "\e6e8";
}

.im-box:before {
    content: "\e6e9";
}

.im-boy:before {
    content: "\e6ea";
}

.im-bra:before {
    content: "\e6eb";
}

.im-brain-2:before {
    content: "\e6ec";
}

.im-brain-3:before {
    content: "\e6ed";
}

.im-brain:before {
    content: "\e6ee";
}

.im-brazil:before {
    content: "\e6ef";
}

.im-bread-2:before {
    content: "\e6f0";
}

.im-bread:before {
    content: "\e6f1";
}

.im-bridge:before {
    content: "\e6f2";
}

.im-brightkite:before {
    content: "\e6f3";
}

.im-broke-link2:before {
    content: "\e6f4";
}

.im-broken-link:before {
    content: "\e6f5";
}

.im-broom:before {
    content: "\e6f6";
}

.im-brush:before {
    content: "\e6f7";
}

.im-bucket:before {
    content: "\e6f8";
}

.im-bug:before {
    content: "\e6f9";
}

.im-building:before {
    content: "\e6fa";
}

.im-bulleted-list:before {
    content: "\e6fb";
}

.im-bus-2:before {
    content: "\e6fc";
}

.im-bus:before {
    content: "\e6fd";
}

.im-business-man:before {
    content: "\e6fe";
}

.im-business-manwoman:before {
    content: "\e6ff";
}

.im-business-mens:before {
    content: "\e700";
}

.im-business-woman:before {
    content: "\e701";
}

.im-butterfly:before {
    content: "\e702";
}

.im-button:before {
    content: "\e703";
}

.im-cable-car:before {
    content: "\e704";
}

.im-cake:before {
    content: "\e705";
}

.im-calculator-2:before {
    content: "\e706";
}

.im-calculator-3:before {
    content: "\e707";
}

.im-calculator:before {
    content: "\e708";
}

.im-calendar-2:before {
    content: "\e709";
}

.im-calendar-3:before, .fa-calendar:before {
    content: "\e70a";
}

.im-calendar-4:before {
    content: "\e70b";
}

.im-calendar-clock:before {
    content: "\e70c";
}

.im-calendar:before {
    content: "\e70d";
}

.im-camel:before {
    content: "\e70e";
}

.im-camera-2:before {
    content: "\e70f";
}

.im-camera-3:before {
    content: "\e710";
}

.im-camera-4:before {
    content: "\e711";
}

.im-camera-5:before {
    content: "\e712";
}

.im-camera-back:before {
    content: "\e713";
}

.im-camera:before {
    content: "\e714";
}

.im-can-2:before {
    content: "\e715";
}

.im-can:before {
    content: "\e716";
}

.im-canada:before {
    content: "\e717";
}

.im-cancer-2:before {
    content: "\e718";
}

.im-cancer-3:before {
    content: "\e719";
}

.im-cancer:before {
    content: "\e71a";
}

.im-candle:before {
    content: "\e71b";
}

.im-candy-cane:before {
    content: "\e71c";
}

.im-candy:before {
    content: "\e71d";
}

.im-cannon:before {
    content: "\e71e";
}

.im-cap-2:before {
    content: "\e71f";
}

.im-cap-3:before {
    content: "\e720";
}

.im-cap-smiley:before {
    content: "\e721";
}

.im-cap:before {
    content: "\e722";
}

.im-capricorn-2:before {
    content: "\e723";
}

.im-capricorn:before {
    content: "\e724";
}

.im-car-2:before {
    content: "\e725";
}

.im-car-3:before {
    content: "\e726";
}

.im-car-coins:before {
    content: "\e727";
}

.im-car-items:before {
    content: "\e728";
}

.im-car-wheel:before {
    content: "\e729";
}

.im-car:before {
    content: "\e72a";
}

.im-cardigan:before {
    content: "\e72b";
}

.im-cardiovascular:before {
    content: "\e72c";
}

.im-cart-quantity:before {
    content: "\e72d";
}

.im-casette-tape:before {
    content: "\e72e";
}

.im-cash-register:before {
    content: "\e72f";
}

.im-cash-register2:before {
    content: "\e730";
}

.im-castle:before {
    content: "\e731";
}

.im-cat:before {
    content: "\e732";
}

.im-cathedral:before {
    content: "\e733";
}

.im-cauldron:before {
    content: "\e734";
}

.im-cd-2:before {
    content: "\e735";
}

.im-cd-cover:before {
    content: "\e736";
}

.im-cd:before {
    content: "\e737";
}

.im-cello:before {
    content: "\e738";
}

.im-celsius:before {
    content: "\e739";
}

.im-chacked-flag:before {
    content: "\e73a";
}

.im-chair:before {
    content: "\e73b";
}

.im-charger:before {
    content: "\e73c";
}

.im-check-2:before {
    content: "\e73d";
}

.im-check:before {
    content: "\e73e";
}

.im-checked-user:before {
    content: "\e73f";
}

.im-checkmate:before {
    content: "\e740";
}

.im-checkout-bag:before {
    content: "\e741";
}

.im-checkout-basket:before {
    content: "\e742";
}

.im-checkout:before {
    content: "\e743";
}

.im-cheese:before {
    content: "\e744";
}

.im-cheetah:before {
    content: "\e745";
}

.im-chef-hat:before {
    content: "\e746";
}

.im-chef-hat2:before {
    content: "\e747";
}

.im-chef:before {
    content: "\e748";
}

.im-chemical-2:before {
    content: "\e749";
}

.im-chemical-3:before {
    content: "\e74a";
}

.im-chemical-4:before {
    content: "\e74b";
}

.im-chemical-5:before {
    content: "\e74c";
}

.im-chemical:before {
    content: "\e74d";
}

.im-chess-board:before {
    content: "\e74e";
}

.im-chess:before {
    content: "\e74f";
}

.im-chicken:before {
    content: "\e750";
}

.im-chile:before {
    content: "\e751";
}

.im-chimney:before {
    content: "\e752";
}

.im-china:before {
    content: "\e753";
}

.im-chinese-temple:before {
    content: "\e754";
}

.im-chip:before {
    content: "\e755";
}

.im-chopsticks-2:before {
    content: "\e756";
}

.im-chopsticks:before {
    content: "\e757";
}

.im-christmas-ball:before {
    content: "\e758";
}

.im-christmas-bell:before {
    content: "\e759";
}

.im-christmas-candle:before {
    content: "\e75a";
}

.im-christmas-hat:before {
    content: "\e75b";
}

.im-christmas-sleigh:before {
    content: "\e75c";
}

.im-christmas-snowman:before {
    content: "\e75d";
}

.im-christmas-sock:before {
    content: "\e75e";
}

.im-christmas-tree:before {
    content: "\e75f";
}

.im-christmas:before {
    content: "\e760";
}

.im-chrome:before {
    content: "\e761";
}

.im-chrysler-building:before {
    content: "\e762";
}

.im-cinema:before {
    content: "\e763";
}

.im-circular-point:before {
    content: "\e764";
}

.im-city-hall:before {
    content: "\e765";
}

.im-clamp:before {
    content: "\e766";
}

.im-clapperboard-close:before {
    content: "\e767";
}

.im-clapperboard-open:before {
    content: "\e768";
}

.im-claps:before {
    content: "\e769";
}

.im-clef:before {
    content: "\e76a";
}

.im-clinic:before {
    content: "\e76b";
}

.im-clock-2:before {
    content: "\e76c";
}

.im-clock-3:before {
    content: "\e76d";
}

.im-clock-4:before {
    content: "\e76e";
}

.im-clock-back:before {
    content: "\e76f";
}

.im-clock-forward:before {
    content: "\e770";
}

.im-clock:before {
    content: "\e771";
}

.im-close-window:before {
    content: "\e772";
}

.im-close:before, .popup-close:before {
    content: "\e773";
}

.im-clothing-store:before {
    content: "\e774";
}

.im-cloud--:before {
    content: "\e775";
}

.im-cloud-:before {
    content: "\e776";
}

.im-cloud-camera:before {
    content: "\e777";
}

.im-cloud-computer:before {
    content: "\e778";
}

.im-cloud-email:before {
    content: "\e779";
}

.im-cloud-hail:before {
    content: "\e77a";
}

.im-cloud-laptop:before {
    content: "\e77b";
}

.im-cloud-lock:before {
    content: "\e77c";
}

.im-cloud-moon:before {
    content: "\e77d";
}

.im-cloud-music:before {
    content: "\e77e";
}

.im-cloud-picture:before {
    content: "\e77f";
}

.im-cloud-rain:before {
    content: "\e780";
}

.im-cloud-remove:before {
    content: "\e781";
}

.im-cloud-secure:before {
    content: "\e782";
}

.im-cloud-settings:before {
    content: "\e783";
}

.im-cloud-smartphone:before {
    content: "\e784";
}

.im-cloud-snow:before {
    content: "\e785";
}

.im-cloud-sun:before {
    content: "\e786";
}

.im-cloud-tablet:before {
    content: "\e787";
}

.im-cloud-video:before {
    content: "\e788";
}

.im-cloud-weather:before {
    content: "\e789";
}

.im-cloud:before {
    content: "\e78a";
}

.im-clouds-weather:before {
    content: "\e78b";
}

.im-clouds:before {
    content: "\e78c";
}

.im-clown:before {
    content: "\e78d";
}

.im-cmyk:before {
    content: "\e78e";
}

.im-coat:before {
    content: "\e78f";
}

.im-cocktail:before {
    content: "\e790";
}

.im-coconut:before {
    content: "\e791";
}

.im-code-window:before {
    content: "\e792";
}

.im-coding:before {
    content: "\e793";
}

.im-coffee-2:before {
    content: "\e794";
}

.im-coffee-bean:before {
    content: "\e795";
}

.im-coffee-machine:before {
    content: "\e796";
}

.im-coffee-togo:before {
    content: "\e797";
}

.im-coffee:before {
    content: "\e798";
}

.im-coffin:before {
    content: "\e799";
}

.im-coin:before {
    content: "\e79a";
}

.im-coins-2:before {
    content: "\e79b";
}

.im-coins-3:before {
    content: "\e79c";
}

.im-coins:before {
    content: "\e79d";
}

.im-colombia:before {
    content: "\e79e";
}

.im-colosseum:before {
    content: "\e79f";
}

.im-column-2:before {
    content: "\e7a0";
}

.im-column-3:before {
    content: "\e7a1";
}

.im-column:before {
    content: "\e7a2";
}

.im-comb-2:before {
    content: "\e7a3";
}

.im-comb:before {
    content: "\e7a4";
}

.im-communication-tower:before {
    content: "\e7a5";
}

.im-communication-tower2:before {
    content: "\e7a6";
}

.im-compass-2:before {
    content: "\e7a7";
}

.im-compass-3:before {
    content: "\e7a8";
}

.im-compass-4:before {
    content: "\e7a9";
}

.im-compass-rose:before {
    content: "\e7aa";
}

.im-compass:before {
    content: "\e7ab";
}

.im-computer-2:before {
    content: "\e7ac";
}

.im-computer-3:before {
    content: "\e7ad";
}

.im-computer-secure:before {
    content: "\e7ae";
}

.im-computer:before {
    content: "\e7af";
}

.im-conference:before {
    content: "\e7b0";
}

.im-confused:before {
    content: "\e7b1";
}

.im-conservation:before {
    content: "\e7b2";
}

.im-consulting:before {
    content: "\e7b3";
}

.im-contrast:before {
    content: "\e7b4";
}

.im-control-2:before {
    content: "\e7b5";
}

.im-control:before {
    content: "\e7b6";
}

.im-cookie-man:before {
    content: "\e7b7";
}

.im-cookies:before {
    content: "\e7b8";
}

.im-cool-guy:before {
    content: "\e7b9";
}

.im-cool:before {
    content: "\e7ba";
}

.im-copyright:before {
    content: "\e7bb";
}

.im-costume:before {
    content: "\e7bc";
}

.im-couple-sign:before {
    content: "\e7bd";
}

.im-cow:before {
    content: "\e7be";
}

.im-cpu:before {
    content: "\e7bf";
}

.im-crane:before {
    content: "\e7c0";
}

.im-cranium:before {
    content: "\e7c1";
}

.im-credit-card:before {
    content: "\e7c2";
}

.im-credit-card2:before {
    content: "\e7c3";
}

.im-credit-card3:before {
    content: "\e7c4";
}

.im-cricket:before {
    content: "\e7c5";
}

.im-criminal:before {
    content: "\e7c6";
}

.im-croissant:before {
    content: "\e7c7";
}

.im-crop-2:before {
    content: "\e7c8";
}

.im-crop-3:before {
    content: "\e7c9";
}

.im-crown-2:before {
    content: "\e7ca";
}

.im-crown:before {
    content: "\e7cb";
}

.im-crying:before {
    content: "\e7cc";
}

.im-cube-molecule:before {
    content: "\e7cd";
}

.im-cube-molecule2:before {
    content: "\e7ce";
}

.im-cupcake:before {
    content: "\e7cf";
}

.im-cursor-click:before {
    content: "\e7d0";
}

.im-cursor-click2:before {
    content: "\e7d1";
}

.im-cursor-move:before {
    content: "\e7d2";
}

.im-cursor-move2:before {
    content: "\e7d3";
}

.im-cursor-select:before {
    content: "\e7d4";
}

.im-cursor:before {
    content: "\e7d5";
}

.im-d-eyeglasses:before {
    content: "\e7d6";
}

.im-d-eyeglasses2:before {
    content: "\e7d7";
}

.im-dam:before {
    content: "\e7d8";
}

.im-danemark:before {
    content: "\e7d9";
}

.im-danger-2:before {
    content: "\e7da";
}

.im-danger:before {
    content: "\e7db";
}

.im-dashboard:before {
    content: "\e7dc";
}

.im-data-backup:before {
    content: "\e7dd";
}

.im-data-block:before {
    content: "\e7de";
}

.im-data-center:before {
    content: "\e7df";
}

.im-data-clock:before {
    content: "\e7e0";
}

.im-data-cloud:before {
    content: "\e7e1";
}

.im-data-compress:before {
    content: "\e7e2";
}

.im-data-copy:before {
    content: "\e7e3";
}

.im-data-download:before {
    content: "\e7e4";
}

.im-data-financial:before {
    content: "\e7e5";
}

.im-data-key:before {
    content: "\e7e6";
}

.im-data-lock:before {
    content: "\e7e7";
}

.im-data-network:before {
    content: "\e7e8";
}

.im-data-password:before {
    content: "\e7e9";
}

.im-data-power:before {
    content: "\e7ea";
}

.im-data-refresh:before {
    content: "\e7eb";
}

.im-data-save:before {
    content: "\e7ec";
}

.im-data-search:before {
    content: "\e7ed";
}

.im-data-security:before {
    content: "\e7ee";
}

.im-data-settings:before {
    content: "\e7ef";
}

.im-data-sharing:before {
    content: "\e7f0";
}

.im-data-shield:before {
    content: "\e7f1";
}

.im-data-signal:before {
    content: "\e7f2";
}

.im-data-storage:before {
    content: "\e7f3";
}

.im-data-stream:before {
    content: "\e7f4";
}

.im-data-transfer:before {
    content: "\e7f5";
}

.im-data-unlock:before {
    content: "\e7f6";
}

.im-data-upload:before {
    content: "\e7f7";
}

.im-data-yes:before {
    content: "\e7f8";
}

.im-data:before {
    content: "\e7f9";
}

.im-david-star:before {
    content: "\e7fa";
}

.im-daylight:before {
    content: "\e7fb";
}

.im-death:before {
    content: "\e7fc";
}

.im-debian:before {
    content: "\e7fd";
}

.im-dec:before {
    content: "\e7fe";
}

.im-decrase-inedit:before {
    content: "\e7ff";
}

.im-deer-2:before {
    content: "\e800";
}

.im-deer:before {
    content: "\e801";
}

.im-delete-file:before {
    content: "\e802";
}

.im-delete-window:before {
    content: "\e803";
}

.im-delicious:before {
    content: "\e804";
}

.im-depression:before {
    content: "\e805";
}

.im-deviantart:before {
    content: "\e806";
}

.im-device-syncwithcloud:before {
    content: "\e807";
}

.im-diamond:before {
    content: "\e808";
}

.im-dice-2:before {
    content: "\e809";
}

.im-dice:before {
    content: "\e80a";
}

.im-digg:before {
    content: "\e80b";
}

.im-digital-drawing:before {
    content: "\e80c";
}

.im-diigo:before {
    content: "\e80d";
}

.im-dinosaur:before {
    content: "\e80e";
}

.im-diploma-2:before {
    content: "\e80f";
}

.im-diploma:before {
    content: "\e810";
}

.im-direction-east:before {
    content: "\e811";
}

.im-direction-north:before {
    content: "\e812";
}

.im-direction-south:before {
    content: "\e813";
}

.im-direction-west:before {
    content: "\e814";
}

.im-director:before {
    content: "\e815";
}

.im-disk:before {
    content: "\e816";
}

.im-dj:before {
    content: "\e817";
}

.im-dna-2:before {
    content: "\e818";
}

.im-dna-helix:before {
    content: "\e819";
}

.im-dna:before {
    content: "\e81a";
}

.im-doctor:before {
    content: "\e81b";
}

.im-dog:before {
    content: "\e81c";
}

.im-dollar-sign:before {
    content: "\e81d";
}

.im-dollar-sign2:before {
    content: "\e81e";
}

.im-dollar:before {
    content: "\e81f";
}

.im-dolphin:before {
    content: "\e820";
}

.im-domino:before {
    content: "\e821";
}

.im-door-hanger:before {
    content: "\e822";
}

.im-door:before {
    content: "\e823";
}

.im-doplr:before {
    content: "\e824";
}

.im-double-circle:before {
    content: "\e825";
}

.im-double-tap:before {
    content: "\e826";
}

.im-doughnut:before {
    content: "\e827";
}

.im-dove:before {
    content: "\e828";
}

.im-down-2:before, .fa-bookmark:before {
    content: "\e829";
}

.im-down-3:before {
    content: "\e82a";
}

.im-down-4:before {
    content: "\e82b";
}

.im-down:before, .side-menu .active > a > .fa.arrow:before, .side-menu .active .plus-times:before {
    content: "\e82c";
}

.im-download-2:before {
    content: "\e82d";
}

.im-download-fromcloud:before {
    content: "\e82e";
}

.im-download-window:before {
    content: "\e82f";
}

.im-download:before {
    content: "\e830";
}

.im-downward:before {
    content: "\e831";
}

.im-drag-down:before {
    content: "\e832";
}

.im-drag-left:before {
    content: "\e833";
}

.im-drag-right:before {
    content: "\e834";
}

.im-drag-up:before {
    content: "\e835";
}

.im-drag:before {
    content: "\e836";
}

.im-dress:before {
    content: "\e837";
}

.im-drill-2:before {
    content: "\e838";
}

.im-drill:before {
    content: "\e839";
}

.im-drop:before {
    content: "\e83a";
}

.im-dropbox:before {
    content: "\e83b";
}

.im-drum:before {
    content: "\e83c";
}

.im-dry:before {
    content: "\e83d";
}

.im-duck:before {
    content: "\e83e";
}

.im-dumbbell:before {
    content: "\e83f";
}

.im-duplicate-layer:before {
    content: "\e840";
}

.im-duplicate-window:before {
    content: "\e841";
}

.im-dvd:before {
    content: "\e842";
}

.im-eagle:before {
    content: "\e843";
}

.im-ear:before {
    content: "\e844";
}

.im-earphones-2:before {
    content: "\e845";
}

.im-earphones:before {
    content: "\e846";
}

.im-eci-icon:before {
    content: "\e847";
}

.im-edit-map:before {
    content: "\e848";
}

.im-edit:before {
    content: "\e849";
}

.im-eggs:before {
    content: "\e84a";
}

.im-egypt:before {
    content: "\e84b";
}

.im-eifel-tower:before {
    content: "\e84c";
}

.im-eject-2:before {
    content: "\e84d";
}

.im-eject:before {
    content: "\e84e";
}

.im-el-castillo:before {
    content: "\e84f";
}

.im-elbow:before {
    content: "\e850";
}

.im-electric-guitar:before {
    content: "\e851";
}

.im-electricity:before {
    content: "\e852";
}

.im-elephant:before {
    content: "\e853";
}

.im-email:before {
    content: "\e854";
}

.im-embassy:before {
    content: "\e855";
}

.im-empire-statebuilding:before {
    content: "\e856";
}

.im-empty-box:before {
    content: "\e857";
}

.im-end2:before {
    content: "\e858";
}

.im-end-2:before {
    content: "\e859";
}

.im-end:before {
    content: "\e85a";
}

.im-endways:before {
    content: "\e85b";
}

.im-engineering:before {
    content: "\e85c";
}

.im-envelope-2:before {
    content: "\e85d";
}

.im-envelope:before {
    content: "\e85e";
}

.im-environmental-2:before {
    content: "\e85f";
}

.im-environmental-3:before {
    content: "\e860";
}

.im-environmental:before {
    content: "\e861";
}

.im-equalizer:before {
    content: "\e862";
}

.im-eraser-2:before {
    content: "\e863";
}

.im-eraser-3:before {
    content: "\e864";
}

.im-eraser:before {
    content: "\e865";
}

.im-error-404window:before {
    content: "\e866";
}

.im-euro-sign:before {
    content: "\e867";
}

.im-euro-sign2:before {
    content: "\e868";
}

.im-euro:before {
    content: "\e869";
}

.im-evernote:before {
    content: "\e86a";
}

.im-evil:before {
    content: "\e86b";
}

.im-explode:before {
    content: "\e86c";
}

.im-eye-2:before {
    content: "\e86d";
}

.im-eye-blind:before {
    content: "\e86e";
}

.im-eye-invisible:before {
    content: "\e86f";
}

.im-eye-scan:before {
    content: "\e870";
}

.im-eye-visible:before {
    content: "\e871";
}

.im-eye:before {
    content: "\e872";
}

.im-eyebrow-2:before {
    content: "\e873";
}

.im-eyebrow-3:before {
    content: "\e874";
}

.im-eyebrow:before {
    content: "\e875";
}

.im-eyeglasses-smiley:before {
    content: "\e876";
}

.im-eyeglasses-smiley2:before {
    content: "\e877";
}

.im-face-style:before {
    content: "\e878";
}

.im-face-style2:before {
    content: "\e879";
}

.im-face-style3:before {
    content: "\e87a";
}

.im-face-style4:before {
    content: "\e87b";
}

.im-face-style5:before {
    content: "\e87c";
}

.im-face-style6:before {
    content: "\e87d";
}

.im-facebook-2:before {
    content: "\e87e";
}

.im-facebook:before {
    content: "\e87f";
}

.im-factory-2:before {
    content: "\e880";
}

.im-factory:before {
    content: "\e881";
}

.im-fahrenheit:before {
    content: "\e882";
}

.im-family-sign:before {
    content: "\e883";
}

.im-fan:before {
    content: "\e884";
}

.im-farmer:before {
    content: "\e885";
}

.im-fashion:before {
    content: "\e886";
}

.im-favorite-window:before {
    content: "\e887";
}

.im-fax:before {
    content: "\e888";
}

.im-feather:before {
    content: "\e889";
}

.im-feedburner:before {
    content: "\e88a";
}

.im-female-2:before {
    content: "\e88b";
}

.im-female-sign:before {
    content: "\e88c";
}

.im-female:before {
    content: "\e88d";
}

.im-file-block:before {
    content: "\e88e";
}

.im-file-bookmark:before {
    content: "\e88f";
}

.im-file-chart:before {
    content: "\e890";
}

.im-file-clipboard:before {
    content: "\e891";
}

.im-file-clipboardfiletext:before {
    content: "\e892";
}

.im-file-clipboardtextimage:before {
    content: "\e893";
}

.im-file-cloud:before {
    content: "\e894";
}

.im-file-copy:before {
    content: "\e895";
}

.im-file-copy2:before {
    content: "\e896";
}

.im-file-csv:before {
    content: "\e897";
}

.im-file-download:before {
    content: "\e898";
}

.im-file-edit:before {
    content: "\e899";
}

.im-file-excel:before {
    content: "\e89a";
}

.im-file-favorite:before {
    content: "\e89b";
}

.im-file-fire:before {
    content: "\e89c";
}

.im-file-graph:before {
    content: "\e89d";
}

.im-file-hide:before {
    content: "\e89e";
}

.im-file-horizontal:before {
    content: "\e89f";
}

.im-file-horizontaltext:before {
    content: "\e8a0";
}

.im-file-html:before {
    content: "\e8a1";
}

.im-file-jpg:before {
    content: "\e8a2";
}

.im-file-link:before {
    content: "\e8a3";
}

.im-file-loading:before {
    content: "\e8a4";
}

.im-file-lock:before {
    content: "\e8a5";
}

.im-file-love:before {
    content: "\e8a6";
}

.im-file-music:before {
    content: "\e8a7";
}

.im-file-network:before {
    content: "\e8a8";
}

.im-file-pictures:before {
    content: "\e8a9";
}

.im-file-pie:before {
    content: "\e8aa";
}

.im-file-presentation:before {
    content: "\e8ab";
}

.im-file-refresh:before {
    content: "\e8ac";
}

.im-file-search:before {
    content: "\e8ad";
}

.im-file-settings:before {
    content: "\e8ae";
}

.im-file-share:before {
    content: "\e8af";
}

.im-file-textimage:before {
    content: "\e8b0";
}

.im-file-trash:before {
    content: "\e8b1";
}

.im-file-txt:before {
    content: "\e8b2";
}

.im-file-upload:before {
    content: "\e8b3";
}

.im-file-video:before {
    content: "\e8b4";
}

.im-file-word:before {
    content: "\e8b5";
}

.im-file-zip:before {
    content: "\e8b6";
}

.im-file:before {
    content: "\e8b7";
}

.im-files:before {
    content: "\e8b8";
}

.im-film-board:before {
    content: "\e8b9";
}

.im-film-cartridge:before {
    content: "\e8ba";
}

.im-film-strip:before {
    content: "\e8bb";
}

.im-film-video:before {
    content: "\e8bc";
}

.im-film:before {
    content: "\e8bd";
}

.im-filter-2:before {
    content: "\e8be";
}

.im-filter:before {
    content: "\e8bf";
}

.im-financial:before {
    content: "\e8c0";
}

.im-find-user:before {
    content: "\e8c1";
}

.im-finger-dragfoursides:before {
    content: "\e8c2";
}

.im-finger-dragtwosides:before {
    content: "\e8c3";
}

.im-finger-print:before {
    content: "\e8c4";
}

.im-finger:before {
    content: "\e8c5";
}

.im-fingerprint-2:before {
    content: "\e8c6";
}

.im-fingerprint:before {
    content: "\e8c7";
}

.im-fire-flame:before {
    content: "\e8c8";
}

.im-fire-flame2:before {
    content: "\e8c9";
}

.im-fire-hydrant:before {
    content: "\e8ca";
}

.im-fire-staion:before {
    content: "\e8cb";
}

.im-firefox:before {
    content: "\e8cc";
}

.im-firewall:before {
    content: "\e8cd";
}

.im-first-aid:before {
    content: "\e8ce";
}

.im-first:before {
    content: "\e8cf";
}

.im-fish-food:before {
    content: "\e8d0";
}

.im-fish:before {
    content: "\e8d1";
}

.im-fit-to:before {
    content: "\e8d2";
}

.im-fit-to2:before {
    content: "\e8d3";
}

.im-five-fingers:before {
    content: "\e8d4";
}

.im-five-fingersdrag:before {
    content: "\e8d5";
}

.im-five-fingersdrag2:before {
    content: "\e8d6";
}

.im-five-fingerstouch:before {
    content: "\e8d7";
}

.im-flag-2:before {
    content: "\e8d8";
}

.im-flag-3:before {
    content: "\e8d9";
}

.im-flag-4:before {
    content: "\e8da";
}

.im-flag-5:before {
    content: "\e8db";
}

.im-flag-6:before {
    content: "\e8dc";
}

.im-flag:before {
    content: "\e8dd";
}

.im-flamingo:before {
    content: "\e8de";
}

.im-flash-2:before {
    content: "\e8df";
}

.im-flash-video:before {
    content: "\e8e0";
}

.im-flash:before {
    content: "\e8e1";
}

.im-flashlight:before {
    content: "\e8e2";
}

.im-flask-2:before {
    content: "\e8e3";
}

.im-flask:before {
    content: "\e8e4";
}

.im-flick:before {
    content: "\e8e5";
}

.im-flickr:before {
    content: "\e8e6";
}

.im-flowerpot:before {
    content: "\e8e7";
}

.im-fluorescent:before {
    content: "\e8e8";
}

.im-fog-day:before {
    content: "\e8e9";
}

.im-fog-night:before {
    content: "\e8ea";
}

.im-folder-add:before {
    content: "\e8eb";
}

.im-folder-archive:before {
    content: "\e8ec";
}

.im-folder-binder:before {
    content: "\e8ed";
}

.im-folder-binder2:before {
    content: "\e8ee";
}

.im-folder-block:before {
    content: "\e8ef";
}

.im-folder-bookmark:before {
    content: "\e8f0";
}

.im-folder-close:before {
    content: "\e8f1";
}

.im-folder-cloud:before {
    content: "\e8f2";
}

.im-folder-delete:before {
    content: "\e8f3";
}

.im-folder-download:before {
    content: "\e8f4";
}

.im-folder-edit:before {
    content: "\e8f5";
}

.im-folder-favorite:before {
    content: "\e8f6";
}

.im-folder-fire:before {
    content: "\e8f7";
}

.im-folder-hide:before {
    content: "\e8f8";
}

.im-folder-link:before {
    content: "\e8f9";
}

.im-folder-loading:before {
    content: "\e8fa";
}

.im-folder-lock:before {
    content: "\e8fb";
}

.im-folder-love:before {
    content: "\e8fc";
}

.im-folder-music:before {
    content: "\e8fd";
}

.im-folder-network:before {
    content: "\e8fe";
}

.im-folder-open:before {
    content: "\e8ff";
}

.im-folder-open2:before {
    content: "\e900";
}

.im-folder-organizing:before {
    content: "\e901";
}

.im-folder-pictures:before {
    content: "\e902";
}

.im-folder-refresh:before {
    content: "\e903";
}

.im-folder-remove-:before {
    content: "\e904";
}

.im-folder-search:before {
    content: "\e905";
}

.im-folder-settings:before {
    content: "\e906";
}

.im-folder-share:before {
    content: "\e907";
}

.im-folder-trash:before {
    content: "\e908";
}

.im-folder-upload:before {
    content: "\e909";
}

.im-folder-video:before {
    content: "\e90a";
}

.im-folder-withdocument:before {
    content: "\e90b";
}

.im-folder-zip:before {
    content: "\e90c";
}

.im-folder:before {
    content: "\e90d";
}

.im-folders:before {
    content: "\e90e";
}

.im-font-color:before {
    content: "\e90f";
}

.im-font-name:before {
    content: "\e910";
}

.im-font-size:before {
    content: "\e911";
}

.im-font-style:before {
    content: "\e912";
}

.im-font-stylesubscript:before {
    content: "\e913";
}

.im-font-stylesuperscript:before {
    content: "\e914";
}

.im-font-window:before {
    content: "\e915";
}

.im-foot-2:before {
    content: "\e916";
}

.im-foot:before {
    content: "\e917";
}

.im-football-2:before {
    content: "\e918";
}

.im-football:before {
    content: "\e919";
}

.im-footprint-2:before {
    content: "\e91a";
}

.im-footprint-3:before {
    content: "\e91b";
}

.im-footprint:before {
    content: "\e91c";
}

.im-forest:before {
    content: "\e91d";
}

.im-fork:before {
    content: "\e91e";
}

.im-formspring:before {
    content: "\e91f";
}

.im-formula:before {
    content: "\e920";
}

.im-forsquare:before {
    content: "\e921";
}

.im-forward:before {
    content: "\e922";
}

.im-fountain-pen:before {
    content: "\e923";
}

.im-four-fingers:before {
    content: "\e924";
}

.im-four-fingersdrag:before {
    content: "\e925";
}

.im-four-fingersdrag2:before {
    content: "\e926";
}

.im-four-fingerstouch:before {
    content: "\e927";
}

.im-fox:before {
    content: "\e928";
}

.im-frankenstein:before {
    content: "\e929";
}

.im-french-fries:before {
    content: "\e92a";
}

.im-friendfeed:before {
    content: "\e92b";
}

.im-friendster:before {
    content: "\e92c";
}

.im-frog:before {
    content: "\e92d";
}

.im-fruits:before {
    content: "\e92e";
}

.im-fuel:before {
    content: "\e92f";
}

.im-full-bag:before {
    content: "\e930";
}

.im-full-basket:before {
    content: "\e931";
}

.im-full-cart:before {
    content: "\e932";
}

.im-full-moon:before {
    content: "\e933";
}

.im-full-screen:before {
    content: "\e934";
}

.im-full-screen2:before {
    content: "\e935";
}

.im-full-view:before {
    content: "\e936";
}

.im-full-view2:before {
    content: "\e937";
}

.im-full-viewwindow:before {
    content: "\e938";
}

.im-function:before {
    content: "\e939";
}

.im-funky:before {
    content: "\e93a";
}

.im-funny-bicycle:before {
    content: "\e93b";
}

.im-furl:before {
    content: "\e93c";
}

.im-gamepad-2:before {
    content: "\e93d";
}

.im-gamepad:before {
    content: "\e93e";
}

.im-gas-pump:before {
    content: "\e93f";
}

.im-gaugage-2:before {
    content: "\e940";
}

.im-gaugage:before {
    content: "\e941";
}

.im-gay:before {
    content: "\e942";
}

.im-gear-2:before {
    content: "\e943";
}

.im-gear:before {
    content: "\e944";
}

.im-gears-2:before {
    content: "\e945";
}

.im-gears:before {
    content: "\e946";
}

.im-geek-2:before {
    content: "\e947";
}

.im-geek:before {
    content: "\e948";
}

.im-gemini-2:before {
    content: "\e949";
}

.im-gemini:before {
    content: "\e94a";
}

.im-genius:before {
    content: "\e94b";
}

.im-gentleman:before {
    content: "\e94c";
}

.im-geo--:before {
    content: "\e94d";
}

.im-geo-:before {
    content: "\e94e";
}

.im-geo-close:before {
    content: "\e94f";
}

.im-geo-love:before {
    content: "\e950";
}

.im-geo-number:before {
    content: "\e951";
}

.im-geo-star:before {
    content: "\e952";
}

.im-geo:before {
    content: "\e953";
}

.im-geo2--:before {
    content: "\e954";
}

.im-geo2-:before {
    content: "\e955";
}

.im-geo2-close:before {
    content: "\e956";
}

.im-geo2-love:before {
    content: "\e957";
}

.im-geo2-number:before {
    content: "\e958";
}

.im-geo2-star:before {
    content: "\e959";
}

.im-geo2:before {
    content: "\e95a";
}

.im-geo3--:before {
    content: "\e95b";
}

.im-geo3-:before {
    content: "\e95c";
}

.im-geo3-close:before {
    content: "\e95d";
}

.im-geo3-love:before {
    content: "\e95e";
}

.im-geo3-number:before {
    content: "\e95f";
}

.im-geo3-star:before {
    content: "\e960";
}

.im-geo3:before {
    content: "\e961";
}

.im-gey:before {
    content: "\e962";
}

.im-gift-box:before {
    content: "\e963";
}

.im-giraffe:before {
    content: "\e964";
}

.im-girl:before {
    content: "\e965";
}

.im-glass-water:before {
    content: "\e966";
}

.im-glasses-2:before {
    content: "\e967";
}

.im-glasses-3:before {
    content: "\e968";
}

.im-glasses:before {
    content: "\e969";
}

.im-global-position:before {
    content: "\e96a";
}

.im-globe-2:before {
    content: "\e96b";
}

.im-globe:before {
    content: "\e96c";
}

.im-gloves:before {
    content: "\e96d";
}

.im-go-bottom:before {
    content: "\e96e";
}

.im-go-top:before {
    content: "\e96f";
}

.im-goggles:before {
    content: "\e970";
}

.im-golf-2:before {
    content: "\e971";
}

.im-golf:before {
    content: "\e972";
}

.im-google-buzz:before {
    content: "\e973";
}

.im-google-drive:before {
    content: "\e974";
}

.im-google-play:before {
    content: "\e975";
}

.im-google-plus:before {
    content: "\e976";
}

.im-google:before {
    content: "\e977";
}

.im-gopro:before {
    content: "\e978";
}

.im-gorilla:before {
    content: "\e979";
}

.im-gowalla:before {
    content: "\e97a";
}

.im-grave:before {
    content: "\e97b";
}

.im-graveyard:before {
    content: "\e97c";
}

.im-greece:before {
    content: "\e97d";
}

.im-green-energy:before {
    content: "\e97e";
}

.im-green-house:before {
    content: "\e97f";
}

.im-guitar:before {
    content: "\e980";
}

.im-gun-2:before {
    content: "\e981";
}

.im-gun-3:before {
    content: "\e982";
}

.im-gun:before {
    content: "\e983";
}

.im-gymnastics:before {
    content: "\e984";
}

.im-hair-2:before {
    content: "\e985";
}

.im-hair-3:before {
    content: "\e986";
}

.im-hair-4:before {
    content: "\e987";
}

.im-hair:before {
    content: "\e988";
}

.im-half-moon:before {
    content: "\e989";
}

.im-halloween-halfmoon:before {
    content: "\e98a";
}

.im-halloween-moon:before {
    content: "\e98b";
}

.im-hamburger:before {
    content: "\e98c";
}

.im-hammer:before {
    content: "\e98d";
}

.im-hand-touch:before {
    content: "\e98e";
}

.im-hand-touch2:before {
    content: "\e98f";
}

.im-hand-touchsmartphone:before {
    content: "\e990";
}

.im-hand:before {
    content: "\e991";
}

.im-hands:before {
    content: "\e992";
}

.im-handshake:before {
    content: "\e993";
}

.im-hanger:before {
    content: "\e994";
}

.im-happy:before {
    content: "\e995";
}

.im-hat-2:before {
    content: "\e996";
}

.im-hat:before {
    content: "\e997";
}

.im-haunted-house:before {
    content: "\e998";
}

.im-hd-video:before {
    content: "\e999";
}

.im-hd:before {
    content: "\e99a";
}

.im-hdd:before {
    content: "\e99b";
}

.im-headphone:before {
    content: "\e99c";
}

.im-headphones:before {
    content: "\e99d";
}

.im-headset:before {
    content: "\e99e";
}

.im-heart-2:before {
    content: "\e99f";
}

.im-heart:before {
    content: "\e9a0";
}

.im-heels-2:before {
    content: "\e9a1";
}

.im-heels:before {
    content: "\e9a2";
}

.im-height-window:before {
    content: "\e9a3";
}

.im-helicopter-2:before {
    content: "\e9a4";
}

.im-helicopter:before {
    content: "\e9a5";
}

.im-helix-2:before {
    content: "\e9a6";
}

.im-hello:before {
    content: "\e9a7";
}

.im-helmet-2:before {
    content: "\e9a8";
}

.im-helmet-3:before {
    content: "\e9a9";
}

.im-helmet:before {
    content: "\e9aa";
}

.im-hipo:before {
    content: "\e9ab";
}

.im-hipster-glasses:before {
    content: "\e9ac";
}

.im-hipster-glasses2:before {
    content: "\e9ad";
}

.im-hipster-glasses3:before {
    content: "\e9ae";
}

.im-hipster-headphones:before {
    content: "\e9af";
}

.im-hipster-men:before {
    content: "\e9b0";
}

.im-hipster-men2:before {
    content: "\e9b1";
}

.im-hipster-men3:before {
    content: "\e9b2";
}

.im-hipster-sunglasses:before {
    content: "\e9b3";
}

.im-hipster-sunglasses2:before {
    content: "\e9b4";
}

.im-hipster-sunglasses3:before {
    content: "\e9b5";
}

.im-hokey:before {
    content: "\e9b6";
}

.im-holly:before {
    content: "\e9b7";
}

.im-home-2:before {
    content: "\e9b8";
}

.im-home-3:before {
    content: "\e9b9";
}

.im-home-4:before {
    content: "\e9ba";
}

.im-home-5:before {
    content: "\e9bb";
}

.im-home-window:before {
    content: "\e9bc";
}

.im-home:before {
    content: "\e9bd";
}

.im-homosexual:before {
    content: "\e9be";
}

.im-honey:before {
    content: "\e9bf";
}

.im-hong-kong:before {
    content: "\e9c0";
}

.im-hoodie:before {
    content: "\e9c1";
}

.im-horror:before {
    content: "\e9c2";
}

.im-horse:before {
    content: "\e9c3";
}

.im-hospital-2:before {
    content: "\e9c4";
}

.im-hospital:before {
    content: "\e9c5";
}

.im-host:before {
    content: "\e9c6";
}

.im-hot-dog:before {
    content: "\e9c7";
}

.im-hotel:before {
    content: "\e9c8";
}

.im-hour:before {
    content: "\e9c9";
}

.im-hub:before {
    content: "\e9ca";
}

.im-humor:before {
    content: "\e9cb";
}

.im-hurt:before {
    content: "\e9cc";
}

.im-ice-cream:before {
    content: "\e9cd";
}

.im-icq:before {
    content: "\e9ce";
}

.im-id-2:before {
    content: "\e9cf";
}

.im-id-3:before {
    content: "\e9d0";
}

.im-id-card:before {
    content: "\e9d1";
}

.im-idea-2:before {
    content: "\e9d2";
}

.im-idea-3:before {
    content: "\e9d3";
}

.im-idea-4:before {
    content: "\e9d4";
}

.im-idea-5:before {
    content: "\e9d5";
}

.im-idea:before {
    content: "\e9d6";
}

.im-identification-badge:before {
    content: "\e9d7";
}

.im-imdb:before {
    content: "\e9d8";
}

.im-inbox-empty:before {
    content: "\e9d9";
}

.im-inbox-forward:before {
    content: "\e9da";
}

.im-inbox-full:before {
    content: "\e9db";
}

.im-inbox-into:before {
    content: "\e9dc";
}

.im-inbox-out:before {
    content: "\e9dd";
}

.im-inbox-reply:before {
    content: "\e9de";
}

.im-inbox:before {
    content: "\e9df";
}

.im-increase-inedit:before {
    content: "\e9e0";
}

.im-indent-firstline:before {
    content: "\e9e1";
}

.im-indent-leftmargin:before {
    content: "\e9e2";
}

.im-indent-rightmargin:before {
    content: "\e9e3";
}

.im-india:before {
    content: "\e9e4";
}

.im-info-window:before {
    content: "\e9e5";
}

.im-information:before {
    content: "\e9e6";
}

.im-inifity:before {
    content: "\e9e7";
}

.im-instagram:before {
    content: "\e9e8";
}

.im-internet-2:before {
    content: "\e9e9";
}

.im-internet-explorer:before {
    content: "\e9ea";
}

.im-internet-smiley:before {
    content: "\e9eb";
}

.im-internet:before {
    content: "\e9ec";
}

.im-ios-apple:before {
    content: "\e9ed";
}

.im-israel:before {
    content: "\e9ee";
}

.im-italic-text:before {
    content: "\e9ef";
}

.im-jacket-2:before {
    content: "\e9f0";
}

.im-jacket:before {
    content: "\e9f1";
}

.im-jamaica:before {
    content: "\e9f2";
}

.im-japan:before {
    content: "\e9f3";
}

.im-japanese-gate:before {
    content: "\e9f4";
}

.im-jeans:before {
    content: "\e9f5";
}

.im-jeep-2:before {
    content: "\e9f6";
}

.im-jeep:before {
    content: "\e9f7";
}

.im-jet:before {
    content: "\e9f8";
}

.im-joystick:before {
    content: "\e9f9";
}

.im-juice:before {
    content: "\e9fa";
}

.im-jump-rope:before {
    content: "\e9fb";
}

.im-kangoroo:before {
    content: "\e9fc";
}

.im-kenya:before {
    content: "\e9fd";
}

.im-key-2:before {
    content: "\e9fe";
}

.im-key-3:before {
    content: "\e9ff";
}

.im-key-lock:before {
    content: "\ea00";
}

.im-key:before {
    content: "\ea01";
}

.im-keyboard:before {
    content: "\ea02";
}

.im-keyboard3:before {
    content: "\ea03";
}

.im-keypad:before {
    content: "\ea04";
}

.im-king-2:before {
    content: "\ea05";
}

.im-king:before {
    content: "\ea06";
}

.im-kiss:before {
    content: "\ea07";
}

.im-knee:before {
    content: "\ea08";
}

.im-knife-2:before {
    content: "\ea09";
}

.im-knife:before {
    content: "\ea0a";
}

.im-knight:before {
    content: "\ea0b";
}

.im-koala:before {
    content: "\ea0c";
}

.im-korea:before {
    content: "\ea0d";
}

.im-lamp:before {
    content: "\ea0e";
}

.im-landscape-2:before {
    content: "\ea0f";
}

.im-landscape:before {
    content: "\ea10";
}

.im-lantern:before {
    content: "\ea11";
}

.im-laptop-2:before {
    content: "\ea12";
}

.im-laptop-3:before {
    content: "\ea13";
}

.im-laptop-phone:before {
    content: "\ea14";
}

.im-laptop-secure:before {
    content: "\ea15";
}

.im-laptop-tablet:before {
    content: "\ea16";
}

.im-laptop:before {
    content: "\ea17";
}

.im-laser:before {
    content: "\ea18";
}

.im-last-fm:before {
    content: "\ea19";
}

.im-last:before {
    content: "\ea1a";
}

.im-laughing:before {
    content: "\ea1b";
}

.im-layer-1635:before {
    content: "\ea1c";
}

.im-layer-1646:before {
    content: "\ea1d";
}

.im-layer-backward:before {
    content: "\ea1e";
}

.im-layer-forward:before {
    content: "\ea1f";
}

.im-leafs-2:before {
    content: "\ea20";
}

.im-leafs:before {
    content: "\ea21";
}

.im-leaning-tower:before {
    content: "\ea22";
}

.im-left--right:before {
    content: "\ea23";
}

.im-left--right3:before {
    content: "\ea24";
}

.im-left-2:before {
    content: "\ea25";
}

.im-left-3:before {
    content: "\ea26";
}

.im-left-4:before {
    content: "\ea27";
}

.im-left-toright:before {
    content: "\ea28";
}

.im-left:before, .pagination .fa-angle-left:before, .porfolio-bar .fa-arrow-left:before, .fa-angle-double-left:before, .fa-angle-left:before,.pagination .fa-angle-double-left:before,.mfp-arrow-left:before  {
    content: "\ea29";
}

.im-leg-2:before {
    content: "\ea2a";
}

.im-leg:before {
    content: "\ea2b";
}

.im-lego:before {
    content: "\ea2c";
}

.im-lemon:before {
    content: "\ea2d";
}

.im-len-2:before {
    content: "\ea2e";
}

.im-len-3:before {
    content: "\ea2f";
}

.im-len:before {
    content: "\ea30";
}

.im-leo-2:before {
    content: "\ea31";
}

.im-leo:before {
    content: "\ea32";
}

.im-leopard:before {
    content: "\ea33";
}

.im-lesbian:before {
    content: "\ea34";
}

.im-lesbians:before {
    content: "\ea35";
}

.im-letter-close:before {
    content: "\ea36";
}

.im-letter-open:before {
    content: "\ea37";
}

.im-letter-sent:before {
    content: "\ea38";
}

.im-libra-2:before {
    content: "\ea39";
}

.im-libra:before {
    content: "\ea3a";
}

.im-library-2:before {
    content: "\ea3b";
}

.im-library:before {
    content: "\ea3c";
}

.im-life-jacket:before {
    content: "\ea3d";
}

.im-life-safer:before {
    content: "\ea3e";
}

.im-light-bulb:before {
    content: "\ea3f";
}

.im-light-bulb2:before {
    content: "\ea40";
}

.im-light-bulbleaf:before {
    content: "\ea41";
}

.im-lighthouse:before {
    content: "\ea42";
}

.im-like-2:before {
    content: "\ea43";
}

.im-like:before {
    content: "\ea44";
}

.im-line-chart:before {
    content: "\ea45";
}

.im-line-chart2:before {
    content: "\ea46";
}

.im-line-chart3:before {
    content: "\ea47";
}

.im-line-chart4:before {
    content: "\ea48";
}

.im-line-spacing:before {
    content: "\ea49";
}

.im-line-spacingtext:before {
    content: "\ea4a";
}

.im-link-2:before {
    content: "\ea4b";
}

.im-link:before {
    content: "\ea4c";
}

.im-linkedin-2:before {
    content: "\ea4d";
}

.im-linkedin:before {
    content: "\ea4e";
}

.im-linux:before {
    content: "\ea4f";
}

.im-lion:before {
    content: "\ea50";
}

.im-livejournal:before {
    content: "\ea51";
}

.im-loading-2:before {
    content: "\ea52";
}

.im-loading-3:before {
    content: "\ea53";
}

.im-loading-window:before {
    content: "\ea54";
}

.im-loading:before {
    content: "\ea55";
}

.im-location-2:before {
    content: "\ea56";
}

.im-location:before {
    content: "\ea57";
}

.im-lock-2:before {
    content: "\ea58";
}

.im-lock-3:before {
    content: "\ea59";
}

.im-lock-user:before {
    content: "\ea5a";
}

.im-lock-window:before {
    content: "\ea5b";
}

.im-lock:before {
    content: "\ea5c";
}

.im-lollipop-2:before {
    content: "\ea5d";
}

.im-lollipop-3:before {
    content: "\ea5e";
}

.im-lollipop:before {
    content: "\ea5f";
}

.im-loop:before {
    content: "\ea60";
}

.im-loud:before {
    content: "\ea61";
}

.im-loudspeaker:before {
    content: "\ea62";
}

.im-love-2:before {
    content: "\ea63";
}

.im-love-user:before {
    content: "\ea64";
}

.im-love-window:before {
    content: "\ea65";
}

.im-love:before {
    content: "\ea66";
}

.im-lowercase-text:before {
    content: "\ea67";
}

.im-luggafe-front:before {
    content: "\ea68";
}

.im-luggage-2:before {
    content: "\ea69";
}

.im-macro:before {
    content: "\ea6a";
}

.im-magic-wand:before {
    content: "\ea6b";
}

.im-magnet:before {
    content: "\ea6c";
}

.im-magnifi-glass-:before {
    content: "\ea6d";
}

.im-magnifi-glass:before {
    content: "\ea6e";
}

.im-magnifi-glass2:before, .fa-search:before {
    content: "\ea6f";
}

.im-mail-2:before {
    content: "\ea70";
}

.im-mail-3:before {
    content: "\ea71";
}

.im-mail-add:before {
    content: "\ea72";
}

.im-mail-attachement:before {
    content: "\ea73";
}

.im-mail-block:before {
    content: "\ea74";
}

.im-mail-delete:before {
    content: "\ea75";
}

.im-mail-favorite:before {
    content: "\ea76";
}

.im-mail-forward:before {
    content: "\ea77";
}

.im-mail-gallery:before {
    content: "\ea78";
}

.im-mail-inbox:before {
    content: "\ea79";
}

.im-mail-link:before {
    content: "\ea7a";
}

.im-mail-lock:before {
    content: "\ea7b";
}

.im-mail-love:before {
    content: "\ea7c";
}

.im-mail-money:before {
    content: "\ea7d";
}

.im-mail-open:before {
    content: "\ea7e";
}

.im-mail-outbox:before {
    content: "\ea7f";
}

.im-mail-password:before {
    content: "\ea80";
}

.im-mail-photo:before {
    content: "\ea81";
}

.im-mail-read:before {
    content: "\ea82";
}

.im-mail-removex:before {
    content: "\ea83";
}

.im-mail-reply:before {
    content: "\ea84";
}

.im-mail-replyall:before {
    content: "\ea85";
}

.im-mail-search:before {
    content: "\ea86";
}

.im-mail-send:before {
    content: "\ea87";
}

.im-mail-settings:before {
    content: "\ea88";
}

.im-mail-unread:before {
    content: "\ea89";
}

.im-mail-video:before {
    content: "\ea8a";
}

.im-mail-withatsign:before {
    content: "\ea8b";
}

.im-mail-withcursors:before {
    content: "\ea8c";
}

.im-mail:before {
    content: "\ea8d";
}

.im-mailbox-empty:before {
    content: "\ea8e";
}

.im-mailbox-full:before {
    content: "\ea8f";
}

.im-male-2:before {
    content: "\ea90";
}

.im-male-sign:before {
    content: "\ea91";
}

.im-male:before {
    content: "\ea92";
}

.im-malefemale:before {
    content: "\ea93";
}

.im-man-sign:before {
    content: "\ea94";
}

.im-management:before {
    content: "\ea95";
}

.im-mans-underwear:before {
    content: "\ea96";
}

.im-mans-underwear2:before {
    content: "\ea97";
}

.im-map-marker:before {
    content: "\ea98";
}

.im-map-marker2:before {
    content: "\ea99";
}

.im-map-marker3:before {
    content: "\ea9a";
}

.im-map:before {
    content: "\ea9b";
}

.im-map2:before {
    content: "\ea9c";
}

.im-marker-2:before {
    content: "\ea9d";
}

.im-marker-3:before {
    content: "\ea9e";
}

.im-marker:before {
    content: "\ea9f";
}

.im-martini-glass:before {
    content: "\eaa0";
}

.im-mask:before {
    content: "\eaa1";
}

.im-master-card:before {
    content: "\eaa2";
}

.im-maximize-window:before {
    content: "\eaa3";
}

.im-maximize:before {
    content: "\eaa4";
}

.im-medal-2:before {
    content: "\eaa5";
}

.im-medal-3:before {
    content: "\eaa6";
}

.im-medal:before {
    content: "\eaa7";
}

.im-medical-sign:before {
    content: "\eaa8";
}

.im-medicine-2:before {
    content: "\eaa9";
}

.im-medicine-3:before {
    content: "\eaaa";
}

.im-medicine:before {
    content: "\eaab";
}

.im-megaphone:before {
    content: "\eaac";
}

.im-memory-card:before {
    content: "\eaad";
}

.im-memory-card2:before {
    content: "\eaae";
}

.im-memory-card3:before {
    content: "\eaaf";
}

.im-men:before {
    content: "\eab0";
}

.im-menorah:before {
    content: "\eab1";
}

.im-mens:before {
    content: "\eab2";
}

.im-metacafe:before {
    content: "\eab3";
}

.im-mexico:before {
    content: "\eab4";
}

.im-mic:before {
    content: "\eab5";
}

.im-microphone-2:before {
    content: "\eab6";
}

.im-microphone-3:before {
    content: "\eab7";
}

.im-microphone-4:before {
    content: "\eab8";
}

.im-microphone-5:before {
    content: "\eab9";
}

.im-microphone-6:before {
    content: "\eaba";
}

.im-microphone-7:before {
    content: "\eabb";
}

.im-microphone:before {
    content: "\eabc";
}

.im-microscope:before {
    content: "\eabd";
}

.im-milk-bottle:before {
    content: "\eabe";
}

.im-mine:before {
    content: "\eabf";
}

.im-minimize-maximize-close-window:before {
    content: "\eac0";
}

.im-minimize-window:before {
    content: "\eac1";
}

.im-minimize:before {
    content: "\eac2";
}

.im-mirror:before {
    content: "\eac3";
}

.im-mixer:before {
    content: "\eac4";
}

.im-mixx:before {
    content: "\eac5";
}

.im-money-2:before {
    content: "\eac6";
}

.im-money-bag:before {
    content: "\eac7";
}

.im-money-smiley:before {
    content: "\eac8";
}

.im-money:before {
    content: "\eac9";
}

.im-monitor-2:before {
    content: "\eaca";
}

.im-monitor-3:before {
    content: "\eacb";
}

.im-monitor-4:before {
    content: "\eacc";
}

.im-monitor-5:before {
    content: "\eacd";
}

.im-monitor-analytics:before {
    content: "\eace";
}

.im-monitor-laptop:before {
    content: "\eacf";
}

.im-monitor-phone:before {
    content: "\ead0";
}

.im-monitor-tablet:before {
    content: "\ead1";
}

.im-monitor-vertical:before {
    content: "\ead2";
}

.im-monitor:before {
    content: "\ead3";
}

.im-monitoring:before {
    content: "\ead4";
}

.im-monkey:before {
    content: "\ead5";
}

.im-monster:before {
    content: "\ead6";
}

.im-morocco:before {
    content: "\ead7";
}

.im-motorcycle:before {
    content: "\ead8";
}

.im-mouse-2:before {
    content: "\ead9";
}

.im-mouse-3:before {
    content: "\eada";
}

.im-mouse-4:before {
    content: "\eadb";
}

.im-mouse-pointer:before {
    content: "\eadc";
}

.im-mouse:before {
    content: "\eadd";
}

.im-moustache-smiley:before {
    content: "\eade";
}

.im-movie-ticket:before {
    content: "\eadf";
}

.im-movie:before {
    content: "\eae0";
}

.im-mp3-file:before {
    content: "\eae1";
}

.im-museum:before {
    content: "\eae2";
}

.im-mushroom:before {
    content: "\eae3";
}

.im-music-note:before {
    content: "\eae4";
}

.im-music-note2:before {
    content: "\eae5";
}

.im-music-note3:before {
    content: "\eae6";
}

.im-music-note4:before {
    content: "\eae7";
}

.im-music-player:before {
    content: "\eae8";
}

.im-mustache-2:before {
    content: "\eae9";
}

.im-mustache-3:before {
    content: "\eaea";
}

.im-mustache-4:before {
    content: "\eaeb";
}

.im-mustache-5:before {
    content: "\eaec";
}

.im-mustache-6:before {
    content: "\eaed";
}

.im-mustache-7:before {
    content: "\eaee";
}

.im-mustache-8:before {
    content: "\eaef";
}

.im-mustache:before {
    content: "\eaf0";
}

.im-mute:before {
    content: "\eaf1";
}

.im-myspace:before {
    content: "\eaf2";
}

.im-navigat-start:before {
    content: "\eaf3";
}

.im-navigate-end:before {
    content: "\eaf4";
}

.im-navigation-leftwindow:before {
    content: "\eaf5";
}

.im-navigation-rightwindow:before {
    content: "\eaf6";
}

.im-nepal:before {
    content: "\eaf7";
}

.im-netscape:before {
    content: "\eaf8";
}

.im-network-window:before {
    content: "\eaf9";
}

.im-network:before {
    content: "\eafa";
}

.im-neutron:before {
    content: "\eafb";
}

.im-new-mail:before {
    content: "\eafc";
}

.im-new-tab:before {
    content: "\eafd";
}

.im-newspaper-2:before {
    content: "\eafe";
}

.im-newspaper:before {
    content: "\eaff";
}

.im-newsvine:before {
    content: "\eb00";
}

.im-next2:before {
    content: "\eb01";
}

.im-next-3:before {
    content: "\eb02";
}

.im-next-music:before {
    content: "\eb03";
}

.im-next:before {
    content: "\eb04";
}

.im-no-battery:before {
    content: "\eb05";
}

.im-no-drop:before {
    content: "\eb06";
}

.im-no-flash:before {
    content: "\eb07";
}

.im-no-smoking:before {
    content: "\eb08";
}

.im-noose:before {
    content: "\eb09";
}

.im-normal-text:before {
    content: "\eb0a";
}

.im-note:before {
    content: "\eb0b";
}

.im-notepad-2:before {
    content: "\eb0c";
}

.im-notepad:before {
    content: "\eb0d";
}

.im-nuclear:before {
    content: "\eb0e";
}

.im-numbering-list:before {
    content: "\eb0f";
}

.im-nurse:before {
    content: "\eb10";
}

.im-office-lamp:before {
    content: "\eb11";
}

.im-office:before {
    content: "\eb12";
}

.im-oil:before {
    content: "\eb13";
}

.im-old-camera:before {
    content: "\eb14";
}

.im-old-cassette:before {
    content: "\eb15";
}

.im-old-clock:before {
    content: "\eb16";
}

.im-old-radio:before {
    content: "\eb17";
}

.im-old-sticky:before {
    content: "\eb18";
}

.im-old-sticky2:before {
    content: "\eb19";
}

.im-old-telephone:before {
    content: "\eb1a";
}

.im-old-tv:before {
    content: "\eb1b";
}

.im-on-air:before {
    content: "\eb1c";
}

.im-on-off-2:before {
    content: "\eb1d";
}

.im-on-off-3:before {
    content: "\eb1e";
}

.im-on-off:before {
    content: "\eb1f";
}

.im-one-finger:before {
    content: "\eb20";
}

.im-one-fingertouch:before {
    content: "\eb21";
}

.im-one-window:before {
    content: "\eb22";
}

.im-open-banana:before {
    content: "\eb23";
}

.im-open-book:before {
    content: "\eb24";
}

.im-opera-house:before {
    content: "\eb25";
}

.im-opera:before {
    content: "\eb26";
}

.im-optimization:before {
    content: "\eb27";
}

.im-orientation-2:before {
    content: "\eb28";
}

.im-orientation-3:before {
    content: "\eb29";
}

.im-orientation:before {
    content: "\eb2a";
}

.im-orkut:before {
    content: "\eb2b";
}

.im-ornament:before {
    content: "\eb2c";
}

.im-over-time:before {
    content: "\eb2d";
}

.im-over-time2:before {
    content: "\eb2e";
}

.im-owl:before {
    content: "\eb2f";
}

.im-pac-man:before {
    content: "\eb30";
}

.im-paint-brush:before {
    content: "\eb31";
}

.im-paint-bucket:before {
    content: "\eb32";
}

.im-paintbrush:before {
    content: "\eb33";
}

.im-palette:before {
    content: "\eb34";
}

.im-palm-tree:before {
    content: "\eb35";
}

.im-panda:before {
    content: "\eb36";
}

.im-panorama:before {
    content: "\eb37";
}

.im-pantheon:before {
    content: "\eb38";
}

.im-pantone:before {
    content: "\eb39";
}

.im-pants:before {
    content: "\eb3a";
}

.im-paper-plane:before {
    content: "\eb3b";
}

.im-paper:before {
    content: "\eb3c";
}

.im-parasailing:before {
    content: "\eb3d";
}

.im-parrot:before {
    content: "\eb3e";
}

.im-password-2shopping:before {
    content: "\eb3f";
}

.im-password-field:before {
    content: "\eb40";
}

.im-password-shopping:before {
    content: "\eb41";
}

.im-password:before {
    content: "\eb42";
}

.im-pause-2:before {
    content: "\eb43";
}

.im-pause:before {
    content: "\eb44";
}

.im-paw:before {
    content: "\eb45";
}

.im-pawn:before {
    content: "\eb46";
}

.im-paypal:before {
    content: "\eb47";
}

.im-pen-2:before {
    content: "\eb48";
}

.im-pen-3:before {
    content: "\eb49";
}

.im-pen-4:before {
    content: "\eb4a";
}

.im-pen-5:before {
    content: "\eb4b";
}

.im-pen-6:before {
    content: "\eb4c";
}

.im-pen:before {
    content: "\eb4d";
}

.im-pencil-ruler:before {
    content: "\eb4e";
}

.im-pencil:before, .fa-pencil:before {
    content: "\eb4f";
}

.im-penguin:before {
    content: "\eb50";
}

.im-pentagon:before {
    content: "\eb51";
}

.im-people-oncloud:before {
    content: "\eb52";
}

.im-pepper-withfire:before {
    content: "\eb53";
}

.im-pepper:before {
    content: "\eb54";
}

.im-petrol:before {
    content: "\eb55";
}

.im-petronas-tower:before {
    content: "\eb56";
}

.im-philipines:before {
    content: "\eb57";
}

.im-phone-2:before {
    content: "\eb58";
}

.im-phone-3:before {
    content: "\eb59";
}

.im-phone-3g:before {
    content: "\eb5a";
}

.im-phone-4g:before {
    content: "\eb5b";
}

.im-phone-simcard:before {
    content: "\eb5c";
}

.im-phone-sms:before {
    content: "\eb5d";
}

.im-phone-wifi:before {
    content: "\eb5e";
}

.im-phone:before {
    content: "\eb5f";
}

.im-photo-2:before {
    content: "\eb60";
}

.im-photo-3:before {
    content: "\eb61";
}

.im-photo-album:before {
    content: "\eb62";
}

.im-photo-album2:before {
    content: "\eb63";
}

.im-photo-album3:before {
    content: "\eb64";
}

.im-photo:before {
    content: "\eb65";
}

.im-photos:before {
    content: "\eb66";
}

.im-physics:before {
    content: "\eb67";
}

.im-pi:before {
    content: "\eb68";
}

.im-piano:before {
    content: "\eb69";
}

.im-picasa:before {
    content: "\eb6a";
}

.im-pie-chart:before {
    content: "\eb6b";
}

.im-pie-chart2:before {
    content: "\eb6c";
}

.im-pie-chart3:before {
    content: "\eb6d";
}

.im-pilates-2:before {
    content: "\eb6e";
}

.im-pilates-3:before {
    content: "\eb6f";
}

.im-pilates:before {
    content: "\eb70";
}

.im-pilot:before {
    content: "\eb71";
}

.im-pinch:before {
    content: "\eb72";
}

.im-ping-pong:before {
    content: "\eb73";
}

.im-pinterest:before {
    content: "\eb74";
}

.im-pipe:before {
    content: "\eb75";
}

.im-pipette:before {
    content: "\eb76";
}

.im-piramids:before {
    content: "\eb77";
}

.im-pisces-2:before {
    content: "\eb78";
}

.im-pisces:before {
    content: "\eb79";
}

.im-pizza-slice:before {
    content: "\eb7a";
}

.im-pizza:before {
    content: "\eb7b";
}

.im-plane-2:before {
    content: "\eb7c";
}

.im-plane:before {
    content: "\eb7d";
}

.im-plant:before {
    content: "\eb7e";
}

.im-plasmid:before {
    content: "\eb7f";
}

.im-plaster:before {
    content: "\eb80";
}

.im-plastic-cupphone:before {
    content: "\eb81";
}

.im-plastic-cupphone2:before {
    content: "\eb82";
}

.im-plate:before {
    content: "\eb83";
}

.im-plates:before {
    content: "\eb84";
}

.im-plaxo:before {
    content: "\eb85";
}

.im-play-music:before {
    content: "\eb86";
}

.im-plug-in:before {
    content: "\eb87";
}

.im-plug-in2:before {
    content: "\eb88";
}

.im-plurk:before {
    content: "\eb89";
}

.im-pointer:before {
    content: "\eb8a";
}

.im-poland:before {
    content: "\eb8b";
}

.im-police-man:before {
    content: "\eb8c";
}

.im-police-station:before {
    content: "\eb8d";
}

.im-police-woman:before {
    content: "\eb8e";
}

.im-police:before {
    content: "\eb8f";
}

.im-polo-shirt:before {
    content: "\eb90";
}

.im-portrait:before {
    content: "\eb91";
}

.im-portugal:before {
    content: "\eb92";
}

.im-post-mail:before {
    content: "\eb93";
}

.im-post-mail2:before {
    content: "\eb94";
}

.im-post-office:before {
    content: "\eb95";
}

.im-post-sign:before {
    content: "\eb96";
}

.im-post-sign2ways:before {
    content: "\eb97";
}

.im-posterous:before {
    content: "\eb98";
}

.im-pound-sign:before {
    content: "\eb99";
}

.im-pound-sign2:before {
    content: "\eb9a";
}

.im-pound:before {
    content: "\eb9b";
}

.im-power-2:before {
    content: "\eb9c";
}

.im-power-3:before {
    content: "\eb9d";
}

.im-power-cable:before {
    content: "\eb9e";
}

.im-power-station:before {
    content: "\eb9f";
}

.im-power:before {
    content: "\eba0";
}

.im-prater:before {
    content: "\eba1";
}

.im-present:before {
    content: "\eba2";
}

.im-presents:before {
    content: "\eba3";
}

.im-press:before {
    content: "\eba4";
}

.im-preview:before {
    content: "\eba5";
}

.im-previous:before {
    content: "\eba6";
}

.im-pricing:before {
    content: "\eba7";
}

.im-printer:before {
    content: "\eba8";
}

.im-professor:before {
    content: "\eba9";
}

.im-profile:before {
    content: "\ebaa";
}

.im-project:before {
    content: "\ebab";
}

.im-projector-2:before {
    content: "\ebac";
}

.im-projector:before {
    content: "\ebad";
}

.im-pulse:before {
    content: "\ebae";
}

.im-pumpkin:before {
    content: "\ebaf";
}

.im-punk:before {
    content: "\ebb0";
}

.im-punker:before {
    content: "\ebb1";
}

.im-puzzle:before {
    content: "\ebb2";
}

.im-qik:before {
    content: "\ebb3";
}

.im-qr-code:before {
    content: "\ebb4";
}

.im-queen-2:before {
    content: "\ebb5";
}

.im-queen:before {
    content: "\ebb6";
}

.im-quill-2:before {
    content: "\ebb7";
}

.im-quill-3:before {
    content: "\ebb8";
}

.im-quill:before {
    content: "\ebb9";
}

.im-quotes-2:before {
    content: "\ebba";
}

.im-quotes:before {
    content: "\ebbb";
}

.im-radio:before {
    content: "\ebbc";
}

.im-radioactive:before {
    content: "\ebbd";
}

.im-rafting:before {
    content: "\ebbe";
}

.im-rain-drop:before {
    content: "\ebbf";
}

.im-rainbow-2:before {
    content: "\ebc0";
}

.im-rainbow:before {
    content: "\ebc1";
}

.im-ram:before {
    content: "\ebc2";
}

.im-razzor-blade:before {
    content: "\ebc3";
}

.im-receipt-2:before {
    content: "\ebc4";
}

.im-receipt-3:before {
    content: "\ebc5";
}

.im-receipt-4:before {
    content: "\ebc6";
}

.im-receipt:before {
    content: "\ebc7";
}

.im-record2:before {
    content: "\ebc8";
}

.im-record-3:before {
    content: "\ebc9";
}

.im-record-music:before {
    content: "\ebca";
}

.im-record:before {
    content: "\ebcb";
}

.im-recycling-2:before {
    content: "\ebcc";
}

.im-recycling:before {
    content: "\ebcd";
}

.im-reddit:before {
    content: "\ebce";
}

.im-redhat:before {
    content: "\ebcf";
}

.im-redirect:before {
    content: "\ebd0";
}

.im-redo:before {
    content: "\ebd1";
}

.im-reel:before {
    content: "\ebd2";
}

.im-refinery:before {
    content: "\ebd3";
}

.im-refresh-window:before {
    content: "\ebd4";
}

.im-refresh:before {
    content: "\ebd5";
}

.im-reload-2:before, .mfp-preloader:before {
    content: "\ebd6";
}

.im-reload-3:before {
    content: "\ebd7";
}

.im-reload:before {
    content: "\ebd8";
}

.im-remote-controll:before {
    content: "\ebd9";
}

.im-remote-controll2:before {
    content: "\ebda";
}

.im-remove-bag:before {
    content: "\ebdb";
}

.im-remove-basket:before {
    content: "\ebdc";
}

.im-remove-cart:before {
    content: "\ebdd";
}

.im-remove-file:before {
    content: "\ebde";
}

.im-remove-user:before {
    content: "\ebdf";
}

.im-remove-window:before {
    content: "\ebe0";
}

.im-remove:before {
    content: "\ebe1";
}

.im-rename:before {
    content: "\ebe2";
}

.im-repair:before {
    content: "\ebe3";
}

.im-repeat-2:before {
    content: "\ebe4";
}

.im-repeat-3:before {
    content: "\ebe5";
}

.im-repeat-4:before {
    content: "\ebe6";
}

.im-repeat-5:before {
    content: "\ebe7";
}

.im-repeat-6:before {
    content: "\ebe8";
}

.im-repeat-7:before {
    content: "\ebe9";
}

.im-repeat:before {
    content: "\ebea";
}

.im-reset:before {
    content: "\ebeb";
}

.im-resize:before {
    content: "\ebec";
}

.im-restore-window:before {
    content: "\ebed";
}

.im-retouching:before {
    content: "\ebee";
}

.im-retro-camera:before {
    content: "\ebef";
}

.im-retro:before {
    content: "\ebf0";
}

.im-retweet:before {
    content: "\ebf1";
}

.im-reverbnation:before {
    content: "\ebf2";
}

.im-rewind:before {
    content: "\ebf3";
}

.im-rgb:before {
    content: "\ebf4";
}

.im-ribbon-2:before {
    content: "\ebf5";
}

.im-ribbon-3:before {
    content: "\ebf6";
}

.im-ribbon:before {
    content: "\ebf7";
}

.im-right-2:before {
    content: "\ebf8";
}

.im-right-3:before {
    content: "\ebf9";
}

.im-right-4:before, .adv-img-button-content .fa-link:before {
    content: "\ebfa";
}

.im-right-toleft:before {
    content: "\ebfb";
}

.im-right:before, .fa-angle-right:before, .porfolio-bar .fa-arrow-right:before, .side-menu .fa.arrow:before, .side-menu .plus-times:before, .side-menu .fa.plus-times:before,
.dropdown-submenu > a:after, .fa-angle-double-right:after,.mfp-arrow-right:before,.advs-box .btn .fa-long-arrow-right:before,.adv-img .btn .fa-long-arrow-right:before {
    content: "\ebfc";
}

.im-road-2:before {
    content: "\ebfd";
}

.im-road-3:before {
    content: "\ebfe";
}

.im-road:before {
    content: "\ebff";
}

.im-robot-2:before {
    content: "\ec00";
}

.im-robot:before {
    content: "\ec01";
}

.im-rock-androll:before {
    content: "\ec02";
}

.im-rocket:before {
    content: "\ec03";
}

.im-roller:before {
    content: "\ec04";
}

.im-roof:before {
    content: "\ec05";
}

.im-rook:before {
    content: "\ec06";
}

.im-rotate-gesture:before {
    content: "\ec07";
}

.im-rotate-gesture2:before {
    content: "\ec08";
}

.im-rotate-gesture3:before {
    content: "\ec09";
}

.im-rotation-390:before {
    content: "\ec0a";
}

.im-rotation:before {
    content: "\ec0b";
}

.im-router-2:before {
    content: "\ec0c";
}

.im-router:before {
    content: "\ec0d";
}

.im-rss:before {
    content: "\ec0e";
}

.im-ruler-2:before {
    content: "\ec0f";
}

.im-ruler:before {
    content: "\ec10";
}

.im-running-shoes:before {
    content: "\ec11";
}

.im-running:before {
    content: "\ec12";
}

.im-safari:before {
    content: "\ec13";
}

.im-safe-box:before {
    content: "\ec14";
}

.im-safe-box2:before {
    content: "\ec15";
}

.im-safety-pinclose:before {
    content: "\ec16";
}

.im-safety-pinopen:before {
    content: "\ec17";
}

.im-sagittarus-2:before {
    content: "\ec18";
}

.im-sagittarus:before {
    content: "\ec19";
}

.im-sailing-ship:before {
    content: "\ec1a";
}

.im-sand-watch:before {
    content: "\ec1b";
}

.im-sand-watch2:before {
    content: "\ec1c";
}

.im-santa-claus:before {
    content: "\ec1d";
}

.im-santa-claus2:before {
    content: "\ec1e";
}

.im-santa-onsled:before {
    content: "\ec1f";
}

.im-satelite-2:before {
    content: "\ec20";
}

.im-satelite:before {
    content: "\ec21";
}

.im-save-window:before {
    content: "\ec22";
}

.im-save:before {
    content: "\ec23";
}

.im-saw:before {
    content: "\ec24";
}

.im-saxophone:before {
    content: "\ec25";
}

.im-scale:before {
    content: "\ec26";
}

.im-scarf:before {
    content: "\ec27";
}

.im-scissor:before {
    content: "\ec28";
}

.im-scooter-front:before {
    content: "\ec29";
}

.im-scooter:before {
    content: "\ec2a";
}

.im-scorpio-2:before {
    content: "\ec2b";
}

.im-scorpio:before {
    content: "\ec2c";
}

.im-scotland:before {
    content: "\ec2d";
}

.im-screwdriver:before {
    content: "\ec2e";
}

.im-scroll-fast:before {
    content: "\ec2f";
}

.im-scroll:before {
    content: "\ec30";
}

.im-scroller-2:before {
    content: "\ec31";
}

.im-scroller:before {
    content: "\ec32";
}

.im-sea-dog:before {
    content: "\ec33";
}

.im-search-oncloud:before {
    content: "\ec34";
}

.im-search-people:before {
    content: "\ec35";
}

.im-secound:before {
    content: "\ec36";
}

.im-secound2:before {
    content: "\ec37";
}

.im-security-block:before {
    content: "\ec38";
}

.im-security-bug:before {
    content: "\ec39";
}

.im-security-camera:before {
    content: "\ec3a";
}

.im-security-check:before {
    content: "\ec3b";
}

.im-security-settings:before {
    content: "\ec3c";
}

.im-security-smiley:before {
    content: "\ec3d";
}

.im-securiy-remove:before {
    content: "\ec3e";
}

.im-seed:before {
    content: "\ec3f";
}

.im-selfie:before {
    content: "\ec40";
}

.im-serbia:before {
    content: "\ec41";
}

.im-server-2:before {
    content: "\ec42";
}

.im-server:before {
    content: "\ec43";
}

.im-servers:before {
    content: "\ec44";
}

.im-settings-window:before {
    content: "\ec45";
}

.im-sewing-machine:before {
    content: "\ec46";
}

.im-sexual:before {
    content: "\ec47";
}

.im-share-oncloud:before {
    content: "\ec48";
}

.im-share-window:before {
    content: "\ec49";
}

.im-share:before {
    content: "\ec4a";
}

.im-sharethis:before, .fa-share-alt:before {
    content: "\ec4b";
}

.im-shark:before {
    content: "\ec4c";
}

.im-sheep:before {
    content: "\ec4d";
}

.im-sheriff-badge:before {
    content: "\ec4e";
}

.im-shield:before {
    content: "\ec4f";
}

.im-ship-2:before {
    content: "\ec50";
}

.im-ship:before {
    content: "\ec51";
}

.im-shirt:before {
    content: "\ec52";
}

.im-shoes-2:before {
    content: "\ec53";
}

.im-shoes-3:before {
    content: "\ec54";
}

.im-shoes:before {
    content: "\ec55";
}

.im-shop-2:before {
    content: "\ec56";
}

.im-shop-3:before {
    content: "\ec57";
}

.im-shop-4:before {
    content: "\ec58";
}

.im-shop:before {
    content: "\ec59";
}

.im-shopping-bag:before,.fa-shopping-cart:before {
    content: "\ec5a";
}

.im-shopping-basket:before {
    content: "\ec5b";
}

.im-shopping-cart:before {
    content: "\ec5c";
}

.im-short-pants:before {
    content: "\ec5d";
}

.im-shoutwire:before {
    content: "\ec5e";
}

.im-shovel:before {
    content: "\ec5f";
}

.im-shuffle-2:before {
    content: "\ec60";
}

.im-shuffle-3:before {
    content: "\ec61";
}

.im-shuffle-4:before {
    content: "\ec62";
}

.im-shuffle:before {
    content: "\ec63";
}

.im-shutter:before {
    content: "\ec64";
}

.im-sidebar-window:before {
    content: "\ec65";
}

.im-signal:before {
    content: "\ec66";
}

.im-singapore:before {
    content: "\ec67";
}

.im-skate-shoes:before {
    content: "\ec68";
}

.im-skateboard-2:before {
    content: "\ec69";
}

.im-skateboard:before {
    content: "\ec6a";
}

.im-skeleton:before {
    content: "\ec6b";
}

.im-ski:before {
    content: "\ec6c";
}

.im-skirt:before {
    content: "\ec6d";
}

.im-skrill:before {
    content: "\ec6e";
}

.im-skull:before {
    content: "\ec6f";
}

.im-skydiving:before {
    content: "\ec70";
}

.im-skype:before {
    content: "\ec71";
}

.im-sled-withgifts:before {
    content: "\ec72";
}

.im-sled:before {
    content: "\ec73";
}

.im-sleeping:before {
    content: "\ec74";
}

.im-sleet:before {
    content: "\ec75";
}

.im-slippers:before {
    content: "\ec76";
}

.im-smart:before {
    content: "\ec77";
}

.im-smartphone-2:before {
    content: "\ec78";
}

.im-smartphone-3:before {
    content: "\ec79";
}

.im-smartphone-4:before {
    content: "\ec7a";
}

.im-smartphone-secure:before {
    content: "\ec7b";
}

.im-smartphone:before {
    content: "\ec7c";
}

.im-smile:before {
    content: "\ec7d";
}

.im-smoking-area:before {
    content: "\ec7e";
}

.im-smoking-pipe:before {
    content: "\ec7f";
}

.im-snake:before {
    content: "\ec80";
}

.im-snorkel:before {
    content: "\ec81";
}

.im-snow-2:before {
    content: "\ec82";
}

.im-snow-dome:before {
    content: "\ec83";
}

.im-snow-storm:before {
    content: "\ec84";
}

.im-snow:before {
    content: "\ec85";
}

.im-snowflake-2:before {
    content: "\ec86";
}

.im-snowflake-3:before {
    content: "\ec87";
}

.im-snowflake-4:before {
    content: "\ec88";
}

.im-snowflake:before {
    content: "\ec89";
}

.im-snowman:before {
    content: "\ec8a";
}

.im-soccer-ball:before {
    content: "\ec8b";
}

.im-soccer-shoes:before {
    content: "\ec8c";
}

.im-socks:before {
    content: "\ec8d";
}

.im-solar:before {
    content: "\ec8e";
}

.im-sound-wave:before {
    content: "\ec8f";
}

.im-sound:before {
    content: "\ec90";
}

.im-soundcloud:before {
    content: "\ec91";
}

.im-soup:before {
    content: "\ec92";
}

.im-south-africa:before {
    content: "\ec93";
}

.im-space-needle:before {
    content: "\ec94";
}

.im-spain:before {
    content: "\ec95";
}

.im-spam-mail:before {
    content: "\ec96";
}

.im-speach-bubble:before {
    content: "\ec97";
}

.im-speach-bubble2:before {
    content: "\ec98";
}

.im-speach-bubble3:before {
    content: "\ec99";
}

.im-speach-bubble4:before {
    content: "\ec9a";
}

.im-speach-bubble5:before {
    content: "\ec9b";
}

.im-speach-bubble6:before, .fa-comment-o:before {
    content: "\ec9c";
}

.im-speach-bubble7:before {
    content: "\ec9d";
}

.im-speach-bubble8:before {
    content: "\ec9e";
}

.im-speach-bubble9:before {
    content: "\ec9f";
}

.im-speach-bubble10:before {
    content: "\eca0";
}

.im-speach-bubble11:before {
    content: "\eca1";
}

.im-speach-bubble12:before {
    content: "\eca2";
}

.im-speach-bubble13:before {
    content: "\eca3";
}

.im-speach-bubbleasking:before {
    content: "\eca4";
}

.im-speach-bubblecomic:before {
    content: "\eca5";
}

.im-speach-bubblecomic2:before {
    content: "\eca6";
}

.im-speach-bubblecomic3:before {
    content: "\eca7";
}

.im-speach-bubblecomic4:before {
    content: "\eca8";
}

.im-speach-bubbledialog:before {
    content: "\eca9";
}

.im-speach-bubbles:before {
    content: "\ecaa";
}

.im-speak-2:before {
    content: "\ecab";
}

.im-speak:before {
    content: "\ecac";
}

.im-speaker-2:before {
    content: "\ecad";
}

.im-speaker:before {
    content: "\ecae";
}

.im-spell-check:before {
    content: "\ecaf";
}

.im-spell-checkabc:before {
    content: "\ecb0";
}

.im-spermium:before {
    content: "\ecb1";
}

.im-spider:before {
    content: "\ecb2";
}

.im-spiderweb:before {
    content: "\ecb3";
}

.im-split-foursquarewindow:before {
    content: "\ecb4";
}

.im-split-horizontal:before {
    content: "\ecb5";
}

.im-split-horizontal2window:before {
    content: "\ecb6";
}

.im-split-vertical:before {
    content: "\ecb7";
}

.im-split-vertical2:before {
    content: "\ecb8";
}

.im-split-window:before {
    content: "\ecb9";
}

.im-spoder:before {
    content: "\ecba";
}

.im-spoon:before {
    content: "\ecbb";
}

.im-sport-mode:before {
    content: "\ecbc";
}

.im-sports-clothings1:before {
    content: "\ecbd";
}

.im-sports-clothings2:before {
    content: "\ecbe";
}

.im-sports-shirt:before {
    content: "\ecbf";
}

.im-spot:before {
    content: "\ecc0";
}

.im-spray:before {
    content: "\ecc1";
}

.im-spread:before {
    content: "\ecc2";
}

.im-spring:before {
    content: "\ecc3";
}

.im-spurl:before {
    content: "\ecc4";
}

.im-spy:before {
    content: "\ecc5";
}

.im-squirrel:before {
    content: "\ecc6";
}

.im-ssl:before {
    content: "\ecc7";
}

.im-st-basilscathedral:before {
    content: "\ecc8";
}

.im-st-paulscathedral:before {
    content: "\ecc9";
}

.im-stamp-2:before {
    content: "\ecca";
}

.im-stamp:before {
    content: "\eccb";
}

.im-stapler:before {
    content: "\eccc";
}

.im-star-track:before {
    content: "\eccd";
}

.im-star:before {
    content: "\ecce";
}

.im-starfish:before {
    content: "\eccf";
}

.im-start2:before {
    content: "\ecd0";
}

.im-start-3:before {
    content: "\ecd1";
}

.im-start-ways:before {
    content: "\ecd2";
}

.im-start:before {
    content: "\ecd3";
}

.im-statistic:before {
    content: "\ecd4";
}

.im-stethoscope:before {
    content: "\ecd5";
}

.im-stop--2:before {
    content: "\ecd6";
}

.im-stop-music:before {
    content: "\ecd7";
}

.im-stop:before {
    content: "\ecd8";
}

.im-stopwatch-2:before {
    content: "\ecd9";
}

.im-stopwatch:before {
    content: "\ecda";
}

.im-storm:before {
    content: "\ecdb";
}

.im-street-view:before {
    content: "\ecdc";
}

.im-street-view2:before {
    content: "\ecdd";
}

.im-strikethrough-text:before {
    content: "\ecde";
}

.im-stroller:before {
    content: "\ecdf";
}

.im-structure:before {
    content: "\ece0";
}

.im-student-female:before {
    content: "\ece1";
}

.im-student-hat:before {
    content: "\ece2";
}

.im-student-hat2:before {
    content: "\ece3";
}

.im-student-male:before {
    content: "\ece4";
}

.im-student-malefemale:before {
    content: "\ece5";
}

.im-students:before {
    content: "\ece6";
}

.im-studio-flash:before {
    content: "\ece7";
}

.im-studio-lightbox:before {
    content: "\ece8";
}

.im-stumbleupon:before {
    content: "\ece9";
}

.im-suit:before {
    content: "\ecea";
}

.im-suitcase:before {
    content: "\eceb";
}

.im-sum-2:before {
    content: "\ecec";
}

.im-sum:before {
    content: "\eced";
}

.im-summer:before {
    content: "\ecee";
}

.im-sun-cloudyrain:before {
    content: "\ecef";
}

.im-sun:before {
    content: "\ecf0";
}

.im-sunglasses-2:before {
    content: "\ecf1";
}

.im-sunglasses-3:before {
    content: "\ecf2";
}

.im-sunglasses-smiley:before {
    content: "\ecf3";
}

.im-sunglasses-smiley2:before {
    content: "\ecf4";
}

.im-sunglasses-w:before {
    content: "\ecf5";
}

.im-sunglasses-w2:before {
    content: "\ecf6";
}

.im-sunglasses-w3:before {
    content: "\ecf7";
}

.im-sunglasses:before {
    content: "\ecf8";
}

.im-sunrise:before {
    content: "\ecf9";
}

.im-sunset:before {
    content: "\ecfa";
}

.im-superman:before {
    content: "\ecfb";
}

.im-support:before {
    content: "\ecfc";
}

.im-surprise:before {
    content: "\ecfd";
}

.im-sushi:before {
    content: "\ecfe";
}

.im-sweden:before {
    content: "\ecff";
}

.im-swimming-short:before {
    content: "\ed00";
}

.im-swimming:before {
    content: "\ed01";
}

.im-swimmwear:before {
    content: "\ed02";
}

.im-switch:before {
    content: "\ed03";
}

.im-switzerland:before {
    content: "\ed04";
}

.im-sync-cloud:before {
    content: "\ed05";
}

.im-sync:before {
    content: "\ed06";
}

.im-synchronize-2:before {
    content: "\ed07";
}

.im-synchronize:before {
    content: "\ed08";
}

.im-t-shirt:before {
    content: "\ed09";
}

.im-tablet-2:before {
    content: "\ed0a";
}

.im-tablet-3:before {
    content: "\ed0b";
}

.im-tablet-orientation:before {
    content: "\ed0c";
}

.im-tablet-phone:before {
    content: "\ed0d";
}

.im-tablet-secure:before {
    content: "\ed0e";
}

.im-tablet-vertical:before {
    content: "\ed0f";
}

.im-tablet:before {
    content: "\ed10";
}

.im-tactic:before {
    content: "\ed11";
}

.im-tag-2:before {
    content: "\ed12";
}

.im-tag-3:before {
    content: "\ed13";
}

.im-tag-4:before {
    content: "\ed14";
}

.im-tag-5:before {
    content: "\ed15";
}

.im-tag:before {
    content: "\ed16";
}

.im-taj-mahal:before {
    content: "\ed17";
}

.im-talk-man:before {
    content: "\ed18";
}

.im-tap:before {
    content: "\ed19";
}

.im-target-market:before {
    content: "\ed1a";
}

.im-target:before {
    content: "\ed1b";
}

.im-taurus-2:before {
    content: "\ed1c";
}

.im-taurus:before {
    content: "\ed1d";
}

.im-taxi-2:before {
    content: "\ed1e";
}

.im-taxi-sign:before {
    content: "\ed1f";
}

.im-taxi:before {
    content: "\ed20";
}

.im-teacher:before {
    content: "\ed21";
}

.im-teapot:before {
    content: "\ed22";
}

.im-technorati:before {
    content: "\ed23";
}

.im-teddy-bear:before {
    content: "\ed24";
}

.im-tee-mug:before {
    content: "\ed25";
}

.im-telephone-2:before {
    content: "\ed26";
}

.im-telephone:before {
    content: "\ed27";
}

.im-telescope:before {
    content: "\ed28";
}

.im-temperature-2:before {
    content: "\ed29";
}

.im-temperature-3:before {
    content: "\ed2a";
}

.im-temperature:before {
    content: "\ed2b";
}

.im-temple:before {
    content: "\ed2c";
}

.im-tennis-ball:before {
    content: "\ed2d";
}

.im-tennis:before {
    content: "\ed2e";
}

.im-tent:before {
    content: "\ed2f";
}

.im-test-tube:before {
    content: "\ed30";
}

.im-test-tube2:before {
    content: "\ed31";
}

.im-testimonal:before {
    content: "\ed32";
}

.im-text-box:before {
    content: "\ed33";
}

.im-text-effect:before {
    content: "\ed34";
}

.im-text-highlightcolor:before {
    content: "\ed35";
}

.im-text-paragraph:before {
    content: "\ed36";
}

.im-thailand:before {
    content: "\ed37";
}

.im-the-whitehouse:before {
    content: "\ed38";
}

.im-this-sideup:before {
    content: "\ed39";
}

.im-thread:before {
    content: "\ed3a";
}

.im-three-arrowfork:before {
    content: "\ed3b";
}

.im-three-fingers:before {
    content: "\ed3c";
}

.im-three-fingersdrag:before {
    content: "\ed3d";
}

.im-three-fingersdrag2:before {
    content: "\ed3e";
}

.im-three-fingerstouch:before {
    content: "\ed3f";
}

.im-thumb:before {
    content: "\ed40";
}

.im-thumbs-downsmiley:before {
    content: "\ed41";
}

.im-thumbs-upsmiley:before {
    content: "\ed42";
}

.im-thunder:before {
    content: "\ed43";
}

.im-thunderstorm:before {
    content: "\ed44";
}

.im-ticket:before {
    content: "\ed45";
}

.im-tie-2:before {
    content: "\ed46";
}

.im-tie-3:before {
    content: "\ed47";
}

.im-tie-4:before {
    content: "\ed48";
}

.im-tie:before {
    content: "\ed49";
}

.im-tiger:before {
    content: "\ed4a";
}

.im-time-backup:before {
    content: "\ed4b";
}

.im-time-bomb:before {
    content: "\ed4c";
}

.im-time-clock:before {
    content: "\ed4d";
}

.im-time-fire:before {
    content: "\ed4e";
}

.im-time-machine:before {
    content: "\ed4f";
}

.im-time-window:before {
    content: "\ed50";
}

.im-timer-2:before {
    content: "\ed51";
}

.im-timer:before {
    content: "\ed52";
}

.im-to-bottom:before {
    content: "\ed53";
}

.im-to-bottom2:before {
    content: "\ed54";
}

.im-to-left:before {
    content: "\ed55";
}

.im-to-right:before {
    content: "\ed56";
}

.im-to-top:before {
    content: "\ed57";
}

.im-to-top2:before {
    content: "\ed58";
}

.im-token-:before {
    content: "\ed59";
}

.im-tomato:before {
    content: "\ed5a";
}

.im-tongue:before {
    content: "\ed5b";
}

.im-tooth-2:before {
    content: "\ed5c";
}

.im-tooth:before {
    content: "\ed5d";
}

.im-top-tobottom:before {
    content: "\ed5e";
}

.im-touch-window:before {
    content: "\ed5f";
}

.im-tourch:before {
    content: "\ed60";
}

.im-tower-2:before {
    content: "\ed61";
}

.im-tower-bridge:before {
    content: "\ed62";
}

.im-tower:before {
    content: "\ed63";
}

.im-trace:before {
    content: "\ed64";
}

.im-tractor:before {
    content: "\ed65";
}

.im-traffic-light:before {
    content: "\ed66";
}

.im-traffic-light2:before {
    content: "\ed67";
}

.im-train-2:before {
    content: "\ed68";
}

.im-train:before {
    content: "\ed69";
}

.im-tram:before {
    content: "\ed6a";
}

.im-transform-2:before {
    content: "\ed6b";
}

.im-transform-3:before {
    content: "\ed6c";
}

.im-transform-4:before {
    content: "\ed6d";
}

.im-transform:before {
    content: "\ed6e";
}

.im-trash-withmen:before {
    content: "\ed6f";
}

.im-tree-2:before {
    content: "\ed70";
}

.im-tree-3:before {
    content: "\ed71";
}

.im-tree-4:before {
    content: "\ed72";
}

.im-tree-5:before {
    content: "\ed73";
}

.im-tree:before {
    content: "\ed74";
}

.im-trekking:before {
    content: "\ed75";
}

.im-triangle-arrowdown:before {
    content: "\ed76";
}

.im-triangle-arrowleft:before {
    content: "\ed77";
}

.im-triangle-arrowright:before {
    content: "\ed78";
}

.im-triangle-arrowup:before {
    content: "\ed79";
}

.im-tripod-2:before {
    content: "\ed7a";
}

.im-tripod-andvideo:before {
    content: "\ed7b";
}

.im-tripod-withcamera:before {
    content: "\ed7c";
}

.im-tripod-withgopro:before {
    content: "\ed7d";
}

.im-trophy-2:before {
    content: "\ed7e";
}

.im-trophy:before {
    content: "\ed7f";
}

.im-truck:before {
    content: "\ed80";
}

.im-trumpet:before {
    content: "\ed81";
}

.im-tumblr:before {
    content: "\ed82";
}

.im-turkey:before {
    content: "\ed83";
}

.im-turn-down:before {
    content: "\ed84";
}

.im-turn-down2:before {
    content: "\ed85";
}

.im-turn-downfromleft:before {
    content: "\ed86";
}

.im-turn-downfromright:before {
    content: "\ed87";
}

.im-turn-left:before {
    content: "\ed88";
}

.im-turn-left3:before {
    content: "\ed89";
}

.im-turn-right:before {
    content: "\ed8a";
}

.im-turn-right3:before {
    content: "\ed8b";
}

.im-turn-up:before {
    content: "\ed8c";
}

.im-turn-up2:before {
    content: "\ed8d";
}

.im-turtle:before {
    content: "\ed8e";
}

.im-tuxedo:before {
    content: "\ed8f";
}

.im-tv:before {
    content: "\ed90";
}

.im-twister:before {
    content: "\ed91";
}

.im-twitter-2:before {
    content: "\ed92";
}

.im-twitter:before {
    content: "\ed93";
}

.im-two-fingers:before {
    content: "\ed94";
}

.im-two-fingersdrag:before {
    content: "\ed95";
}

.im-two-fingersdrag2:before {
    content: "\ed96";
}

.im-two-fingersscroll:before {
    content: "\ed97";
}

.im-two-fingerstouch:before {
    content: "\ed98";
}

.im-two-windows:before {
    content: "\ed99";
}

.im-type-pass:before {
    content: "\ed9a";
}

.im-ukraine:before {
    content: "\ed9b";
}

.im-umbrela:before {
    content: "\ed9c";
}

.im-umbrella-2:before {
    content: "\ed9d";
}

.im-umbrella-3:before {
    content: "\ed9e";
}

.im-under-linetext:before {
    content: "\ed9f";
}

.im-undo:before {
    content: "\eda0";
}

.im-united-kingdom:before {
    content: "\eda1";
}

.im-united-states:before {
    content: "\eda2";
}

.im-university-2:before {
    content: "\eda3";
}

.im-university:before {
    content: "\eda4";
}

.im-unlike-2:before {
    content: "\eda5";
}

.im-unlike:before {
    content: "\eda6";
}

.im-unlock-2:before {
    content: "\eda7";
}

.im-unlock-3:before {
    content: "\eda8";
}

.im-unlock:before {
    content: "\eda9";
}

.im-up--down:before {
    content: "\edaa";
}

.im-up--down3:before {
    content: "\edab";
}

.im-up-2:before {
    content: "\edac";
}

.im-up-3:before {
    content: "\edad";
}

.im-up-4:before {
    content: "\edae";
}

.im-up:before {
    content: "\edaf";
}

.im-upgrade:before {
    content: "\edb0";
}

.im-upload-2:before {
    content: "\edb1";
}

.im-upload-tocloud:before {
    content: "\edb2";
}

.im-upload-window:before {
    content: "\edb3";
}

.im-upload:before {
    content: "\edb4";
}

.im-uppercase-text:before {
    content: "\edb5";
}

.im-upward:before {
    content: "\edb6";
}

.im-url-window:before {
    content: "\edb7";
}

.im-usb-2:before {
    content: "\edb8";
}

.im-usb-cable:before {
    content: "\edb9";
}

.im-usb:before {
    content: "\edba";
}

.im-user:before {
    content: "\edbb";
}

.im-ustream:before {
    content: "\edbc";
}

.im-vase:before {
    content: "\edbd";
}

.im-vector-2:before {
    content: "\edbe";
}

.im-vector-3:before {
    content: "\edbf";
}

.im-vector-4:before {
    content: "\edc0";
}

.im-vector-5:before {
    content: "\edc1";
}

.im-vector:before {
    content: "\edc2";
}

.im-venn-diagram:before {
    content: "\edc3";
}

.im-vest-2:before {
    content: "\edc4";
}

.im-vest:before {
    content: "\edc5";
}

.im-viddler:before {
    content: "\edc6";
}

.im-video-2:before {
    content: "\edc7";
}

.im-video-3:before {
    content: "\edc8";
}

.im-video-4:before {
    content: "\edc9";
}

.im-video-5:before {
    content: "\edca";
}

.im-video-6:before {
    content: "\edcb";
}

.im-video-gamecontroller:before {
    content: "\edcc";
}

.im-video-len:before {
    content: "\edcd";
}

.im-video-len2:before {
    content: "\edce";
}

.im-video-photographer:before {
    content: "\edcf";
}

.im-video-tripod:before {
    content: "\edd0";
}

.im-video:before {
    content: "\edd1";
}

.im-vietnam:before {
    content: "\edd2";
}

.im-view-height:before {
    content: "\edd3";
}

.im-view-width:before {
    content: "\edd4";
}

.im-vimeo:before {
    content: "\edd5";
}

.im-virgo-2:before {
    content: "\edd6";
}

.im-virgo:before {
    content: "\edd7";
}

.im-virus-2:before {
    content: "\edd8";
}

.im-virus-3:before {
    content: "\edd9";
}

.im-virus:before {
    content: "\edda";
}

.im-visa:before {
    content: "\eddb";
}

.im-voice:before {
    content: "\eddc";
}

.im-voicemail:before {
    content: "\eddd";
}

.im-volleyball:before {
    content: "\edde";
}

.im-volume-down:before {
    content: "\eddf";
}

.im-volume-up:before {
    content: "\ede0";
}

.im-vpn:before {
    content: "\ede1";
}

.im-wacom-tablet:before {
    content: "\ede2";
}

.im-waiter:before {
    content: "\ede3";
}

.im-walkie-talkie:before {
    content: "\ede4";
}

.im-wallet-2:before {
    content: "\ede5";
}

.im-wallet-3:before {
    content: "\ede6";
}

.im-wallet:before {
    content: "\ede7";
}

.im-warehouse:before {
    content: "\ede8";
}

.im-warning-window:before {
    content: "\ede9";
}

.im-watch-2:before {
    content: "\edea";
}

.im-watch-3:before {
    content: "\edeb";
}

.im-watch:before {
    content: "\edec";
}

.im-wave-2:before {
    content: "\eded";
}

.im-wave:before {
    content: "\edee";
}

.im-webcam:before {
    content: "\edef";
}

.im-weight-lift:before {
    content: "\edf0";
}

.im-wheelbarrow:before {
    content: "\edf1";
}

.im-wheelchair:before {
    content: "\edf2";
}

.im-width-window:before {
    content: "\edf3";
}

.im-wifi-2:before {
    content: "\edf4";
}

.im-wifi-keyboard:before {
    content: "\edf5";
}

.im-wifi:before {
    content: "\edf6";
}

.im-wind-turbine:before {
    content: "\edf7";
}

.im-windmill:before {
    content: "\edf8";
}

.im-window-2:before {
    content: "\edf9";
}

.im-window:before {
    content: "\edfa";
}

.im-windows-2:before {
    content: "\edfb";
}

.im-windows-microsoft:before {
    content: "\edfc";
}

.im-windows:before {
    content: "\edfd";
}

.im-windsock:before {
    content: "\edfe";
}

.im-windy:before {
    content: "\edff";
}

.im-wine-bottle:before {
    content: "\ee00";
}

.im-wine-glass:before {
    content: "\ee01";
}

.im-wink:before {
    content: "\ee02";
}

.im-winter-2:before {
    content: "\ee03";
}

.im-winter:before {
    content: "\ee04";
}

.im-wireless:before {
    content: "\ee05";
}

.im-witch-hat:before {
    content: "\ee06";
}

.im-witch:before {
    content: "\ee07";
}

.im-wizard:before {
    content: "\ee08";
}

.im-wolf:before {
    content: "\ee09";
}

.im-woman-sign:before {
    content: "\ee0a";
}

.im-womanman:before {
    content: "\ee0b";
}

.im-womans-underwear:before {
    content: "\ee0c";
}

.im-womans-underwear2:before {
    content: "\ee0d";
}

.im-women:before {
    content: "\ee0e";
}

.im-wonder-woman:before {
    content: "\ee0f";
}

.im-wordpress:before {
    content: "\ee10";
}

.im-worker-clothes:before {
    content: "\ee11";
}

.im-worker:before {
    content: "\ee12";
}

.im-wrap-text:before {
    content: "\ee13";
}

.im-wreath:before {
    content: "\ee14";
}

.im-wrench:before {
    content: "\ee15";
}

.im-x-box:before {
    content: "\ee16";
}

.im-x-ray:before {
    content: "\ee17";
}

.im-xanga:before {
    content: "\ee18";
}

.im-xing:before {
    content: "\ee19";
}

.im-yacht:before {
    content: "\ee1a";
}

.im-yahoo-buzz:before {
    content: "\ee1b";
}

.im-yahoo:before {
    content: "\ee1c";
}

.im-yelp:before {
    content: "\ee1d";
}

.im-yes:before {
    content: "\ee1e";
}

.im-ying-yang:before {
    content: "\ee1f";
}

.im-youtube:before {
    content: "\ee20";
}

.im-z-a:before {
    content: "\ee21";
}

.im-zebra:before {
    content: "\ee22";
}

.im-zombie:before {
    content: "\ee23";
}

.im-zoom-gesture:before {
    content: "\ee24";
}

.im-zootool:before {
    content: "\ee25";
}
