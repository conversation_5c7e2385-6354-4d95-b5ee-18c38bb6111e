/*
 * jQuery FlexSlider v2.4.0
 * http://www.woothemes.com/flexslider/
 *
 * Copyright 2012 WooThemes
 * Free to use under the GPLv2 and later license.
 * http://www.gnu.org/licenses/gpl-2.0.html
 *
 * Contributing author: <PERSON> (@mbmufffin)
 *
 */
/* ====================================================================================================================
 * FONT-FACE
 * ====================================================================================================================
*/
@font-face {
    font-family: 'flexslider-icon';
    src: url('fonts/flexslider-icon.eot');
    src: url('fonts/flexslider-icon.eot?#iefix') format('embedded-opentype'), url('fonts/flexslider-icon.woff') format('woff'), url('fonts/flexslider-icon.ttf') format('truetype'), url('fonts/flexslider-icon.svg#flexslider-icon') format('svg');
    font-weight: normal;
    font-style: normal;
}
/* ====================================================================================================================
 * RESETS
 * ====================================================================================================================

*/
.flex-container a:hover,
.flex-slider a:hover,
.flex-container a:focus,
.flex-slider a:focus {
    outline: none;
}

.slides,
.slides > li,
.flex-control-nav,
.flex-direction-nav {
    margin: 0;
    padding: 0;
    list-style: none;
}

.flex-pauseplay span {
    text-transform: capitalize;
}
/* ====================================================================================================================
 * BASE STYLES
 * ===================================================================================================================
*/
.flexslider {
    margin: 0;
    padding: 0;
}

    .flexslider .slides > li {
        display: none;
        -webkit-backface-visibility: hidden;
        height: 100%;
        transform: translate3d(0,0,0);
        overflow: hidden;
    }

    .flexslider li > img, .flexslider li > a > img, .flexslider li > a > img {
        width: 100%;
        display: block;
    }

    .flexslider .slides:after {
        content: "\0020";
        display: block;
        clear: both;
        visibility: hidden;
        line-height: 0;
        height: 0;
    }

html[xmlns] .flexslider .slides {
    display: block;
}

* html .flexslider .slides {
    height: 1%;
}

.no-js .flexslider .slides > li:first-child {
    display: block;
}
/* ====================================================================================================================
 * DEFAULT THEME
 * ====================================================================================================================*/
.flexslider {
    margin: 0 0 23px 0;
    margin-bottom: 33px !important;
    position: relative;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

    .flexslider.no-navs {
        margin-bottom: 0 !important;
    }

    .flexslider .slides img {
        height: auto;
        max-width: none;
    }
    .flexslider .slides [class*="col-md-"] > img {
        max-width: 100%;
    }
    .flexslider .slides .img-box:not(.adv-img) img {
        max-width: 100%;
    }

.flex-direction-nav li a {
    height: auto !important;
}

.flex-viewport {
    max-height: 5000px;
    -webkit-transition: all 1s ease;
    -moz-transition: all 1s ease;
    -ms-transition: all 1s ease;
    -o-transition: all 1s ease;
    transition: all 1s ease;
    max-height: 100%;
}

.loading .flex-viewport {
    max-height: 300px;
}

.flex-direction-nav {
    *
    height: 0;
}

    .flex-direction-nav a {
        text-decoration: none;
        display: block;
        margin: -29px 10px 0 10px;
        position: absolute;
        top: 50%;
        text-align: center;
        z-index: 10;
        overflow: hidden;
        opacity: 0;
        cursor: pointer;
        color: rgb(80, 80, 80);
        -webkit-transition: all 0.3s ease-in-out;
        -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
        width: 60px;
        height: 57px;
    }

        .flex-direction-nav a:before {
            font-family: "FontAwesome";
            font-size: 55px;
            line-height: 55px;
            display: inline-block;
            content: '\f104';
            color: #333;
            text-shadow: 0px 0px 2px #FFF;
        }

.flexslider.white .flex-direction-nav li a:before {
    color: #FFF;
    text-shadow: 0px 0px 2px #000;
}

.flex-direction-nav a.flex-next:before {
    content: '\f105';
}

.flex-direction-nav .flex-prev {
    left: -50px;
}

.flex-direction-nav .flex-next {
    right: -50px;
}

.flexslider:hover .flex-direction-nav .flex-prev {
    opacity: 1;
    left: 0;
}

.flexslider.outer-navs:hover .flex-direction-nav .flex-prev {
    left: 40px;
}

.flexslider:hover .flex-direction-nav .flex-prev:hover {
    opacity: 1;
}

.flexslider:hover .flex-direction-nav .flex-next {
    opacity: 1;
    right: 0;
}

    .flexslider:hover .flex-direction-nav .flex-next:hover {
        opacity: 1;
    }

.flexslider.outer-navs:hover .flex-direction-nav .flex-next {
    right: 40px;
}

.flex-direction-nav .flex-disabled {
    opacity: 0 !important;
    filter: alpha(opacity=0);
    cursor: default;
}

.flex-pauseplay a {
    display: block;
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 5px;
    left: 10px;
    opacity: 0.8;
    z-index: 10;
    overflow: hidden;
    cursor: pointer;
    color: #000;
}

    .flex-pauseplay a:before {
        font-family: "flexslider-icon";
        font-size: 20px;
        display: inline-block;
        content: '\f004';
    }

    .flex-pauseplay a:hover {
        opacity: 1;
    }

    .flex-pauseplay a .flex-play:before {
        content: '\f003';
    }

.flex-control-nav {
    width: 100%;
    position: absolute;
    bottom: -40px;
    text-align: center;
    z-index: 9;
    height: auto !important;
}

    .flex-control-nav li {
        margin: 0 6px;
        display: inline-block;
        zoom: 1;
        *
        display: inline;
        height: auto !important;
    }

.flex-control-paging li a {
    width: 11px;
    height: 11px !important;
    display: block;
    background: rgba(181, 181, 181, 0.9);
    cursor: pointer;
    opacity: .9;
    text-indent: -9999px;
    -moz-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    -o-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    -moz-border-radius: 20px;
    border-radius: 20px;
}

    .flex-control-paging li a:hover {
        background: rgba(134, 134, 134, 0.7);
    }

    .flex-control-paging li a.flex-active {
        background: #DCDCDC;
        cursor: default;
        opacity: 1;
    }

.flex-control-thumbs {
    margin: 5px 0 0;
    position: static;
    overflow: hidden;
}

    .flex-control-thumbs li {
        width: 25%;
        float: left;
        margin: 0;
        overflow: hidden;
    }

    .flex-control-thumbs img {
        width: 100%;
        height: auto;
        display: block;
        opacity: .6;
        cursor: pointer;
        -webkit-transition: all 1s ease;
        -moz-transition: all 1s ease;
        -ms-transition: all 1s ease;
        -o-transition: all 1s ease;
        transition: all 1s ease;
    }

        .flex-control-thumbs img:hover {
            opacity: 1;
        }

    .flex-control-thumbs .flex-active {
        opacity: 1;
        cursor: default;
    }


/*
* Pixor - Copyright (c) Federico Schiocchet - Pixor (www.pixor.it) - Framework Y (www.framework-y.com)
*/
.flexslider .slides {
    overflow: hidden;
}

.flexslider.nav-inner .flex-control-nav {
    bottom: 10px;
}

.flexslider.nav-inner {
    margin-bottom: 0 !important;
}

.flexslider li .circle .caption, .flexslider li .caption-bg, .advs-box .row, .advs-box h4, .advs-box p, .advs-box h3, .advs-box .name-box {
    transform: translate3d(0,0,0);
}

.flexslider.caption .slides li {
    position: relative;
}

.flexslider.carousel.nav-thumb .slides li {
    cursor: pointer;
    overflow: hidden;
    opacity: 0.6;
    transition: all .3s;
}

.flexslider.carousel.nav-thumb li.flex-active-slide {
    cursor: default;
    opacity: 1;
}

.flexslider.carousel.nav-thumb li:hover {
    opacity: 1;
}

.flexslider .advs-box-top-icon i {
    transform: scale(1) !important;
    animation: none !important;
}

.flexslider.carousel.nav-thumb li:hover img, .flex-control-thumbs li:hover img {
    transition: all .3s;
}

.flexslider.carousel.nav-thumb li img, .flex-control-thumbs li img {
    transition: all .3s;
    transform: translate3d(0,0,0);
}

.flexslider.carousel.nav-thumb .slides li .thumb {
    padding: 7px;
    text-align: center;
    border-top: 1px solid #A5A5A5;
    border-bottom: 1px solid #A5A5A5;
}

    .flexslider.carousel.nav-thumb .slides li .thumb h3 {
        text-transform: uppercase;
        font-size: 12px;
        font-weight: 600;
        margin: 1px;
    }

    .flexslider.carousel.nav-thumb .slides li .thumb p {
        font-size: 12px;
        margin: 0;
        color: #777;
    }

.flexslider.visible-dir-nav a.flex-next {
    right: 10px;
    opacity: 1;
}

.flexslider.visible-dir-nav a.flex-prev {
    left: 10px;
    opacity: 1;
}

.flexslider .img-box.circle img {
    width: 100% !important;
    max-width: 100% !important;
}

.flexslider.center {
    margin-left: auto !important;
    margin-right: auto !important;
}

.flexslider.outer-navs a.flex-next {
    transform: translateX(100px);
    width: 50px;
    background: none;
    box-shadow: none;
}

.flexslider.outer-navs a.flex-prev {
    transform: translateX(-100px);
    width: 50px;
    background: none;
    box-shadow: none;
}

.header-slider .flexslider .slides {
    height: 350px;
    overflow: hidden;
    margin: 0;
}

.header-slider .flexslider {
    margin: 0;
    margin-bottom: 0 !important;
}

body > .header-slider .flex-direction-nav {
    display: none;
}

.carousel.nav-thumb {
    margin-top: 5px;
}

.flexslider li .img-box.thumbnail, .flexslider li .advs-box.boxed {
    margin-right: 1px;
}

[class*="row-"].flexslider * {
    height: 100%;
}

.slides li > .advs-box {
    position: relative;
}

.background-page .flex-control-paging {
    display: none;
}

.full-screen-title .flexslider .flex-direction-nav {
    display: none;
}

.header-video.header-parallax .overlaybox .flexslider {
    top: 67px;
}

.full-screen-title.header-parallax .flex-control-nav {
    position: fixed;
}

.header-parallax:not(.full-screen-title) .flex-control-nav {
    bottom: auto;
    top: 325px;
}

.header-slider .layer-parallax {
    height: 490px;
    top: auto;
}



.header-slider .slides li, .header-slider .flex-viewport, .header-slider .slides, .header-slider .flexslider {
    position: relative;
    height: 100%;
    padding: 0;
    margin: 0;
}

.section-slider > .flexslider {
    position: absolute;
    height: 100%;
    top: 0;
    width: 100%;
    right: 0;
    bottom: 0;
    padding: 0;
    margin: 0;
    left: 0;
}

    .section-slider > .flexslider .flex-viewport {
        height: 100%;
    }

.section-slider .overlaybox {
    position: relative;
    z-index: 9;
    width: 100%;
    margin-bottom: 70px;
}

.full-screen-title .flexslider, .full-screen-title .flexslider .flex-viewport, .full-screen-title .flexslider .slides, .full-screen-title .flexslider .slides li, .flexslider .slides .bg-cover {
    padding: 0;
    height: 100% !important;
}
.flexslider .slides .bg-cover,.header-title .flexslider {
    padding: 0;
    height: 100%;
}
.header-slider .bg-overlay {
    position: absolute;
    z-index: 9;
}

.section-slider, .section-two-blocks, .section-bg-video, .section-map {
    overflow: hidden;
    position: relative;
    z-index: 0;
}

    .section-slider > .flexslider .slides, .section-slider > .flexslider .slides li {
        height: 100%;
    }

    .section-slider > .flexslider .flex-control-nav {
        bottom: 10px;
        z-index: 11;
    }

@media (min-width: 992px) {
    .section-slider > .flexslider .flexslider:not(.advanced-slider) .flex-direction-nav a {
        bottom: 0;
        top: auto;
    }

    .section-slider > .flexslider .flexslider:not(.advanced-slider) .flex-direction-nav .flex-prev {
        left: auto !important;
        right: 40px !important;
    }

    .section-slider > .flexslider .flexslider:not(.advanced-slider) .flex-direction-nav .flex-next {
        right: 0 !important;
    }
}

[class*="row-"] .flexslider.advanced-slider .container-inner div {
    height: auto;
}

.section-slider > .advanced-slider, .advanced-slider .section-slide {
    position: static;
    margin-bottom: 0 !important;
}

    .advanced-slider .section-slide .container {
        padding-top: 0;
        padding-bottom: 0;
    }

.section-slide {
    top: 0;
    position: absolute;
    height: 100%;
    width: 100%;
    animation: all .3s;
    overflow: hidden;
}

.container-inner > .hc_column_cnt {
    padding-left: 0;
    padding-right: 0;
}

.section-slide .container {
    padding: 15px;
    height: 100%;
    transform: translate3d(0,0,0);
    max-width: 100%;
    z-index: 9;
}

.section-slide .container-middle {
    display: table !important;
    vertical-align: middle;
    width: 100%;
    height: 100%;
    transform: translate3d(0,0,0);
    position: relative;
}

.section-slide .container-inner {
    display: table-cell !important;
    vertical-align: middle;
    position: relative;
    transform: translate3d(0,0,0);
}


.section-slide .block-right {
    position: absolute;
    right: 0;
}

.section-slide .bg-cover {
    position: absolute;
    width: 100%;
}

.pos-slider {
    height: auto !important;
    width: auto !important;
    position: absolute;
}

.pos-bottom {
    bottom: 0;
}

.pos-left {
    left: 0;
}

.pos-right {
    right: 0;
}

.pos-top {
    top: 0;
}

.pos-center {
    left: 50%;
}

.pos-middle {
    top: 50%;
}

.section-two-blocks .flexslider, .section-two-blocks .flexslider .slides, .section-two-blocks .flexslider .slides li a, .section-two-blocks .flex-viewport {
    height: 100%;
    margin: 0;
}

.section-two-blocks .flex-direction-nav a {
    text-align: center;
}

.section-two-blocks:not(.blocks-right) .flex-direction-nav a.flex-prev {
    margin-left: 23px;
}

.section-two-blocks.blocks-right .flex-direction-nav a.flex-next {
    margin-right: 23px;
    margin-left: 0;
}

.slides > li > a.img-box {
    width: 100%;
    display: block;
}

.social-feed-tw .slides img, .social-feed-fb .slides img {
    width: auto !important;
    display: block;
}

.png-over .slides > li {
    text-align: center;
}

    .png-over .slides > li > img {
        margin: auto;
        max-width: 100%;
    }



/*
==============================
MOBILE - PHONE - Extra small devices
==============================
*/
@media (max-width: 992px) {
    .flex-control-nav, .flexslider.nav-inner .flex-control-nav {
        padding-right: 5px;
        padding-left: 5px;
        bottom: -40px;
    }

    .section-slide .container-middle, .section-slide .container-inner {
        max-width: 100%;
        table-layout: fixed;
        padding-top: 10px;
    }

    .flex-direction-nav {
        display: none;
    }

    .header-slider .layer-parallax {
        position: static !important;
        margin-top: 0 !important;
    }

    .niche-box-blog .flex-control-nav, .niche-box-blog .flex-direction-nav, .niche-box-post .flex-control-nav, .niche-box-post .flex-direction-nav {
        display: none;
    }

    .full-screen-title.header-parallax .flex-control-nav {
        bottom: 10px;
    }



    .flexslider .slides > li {
        height: auto;
    }

    .nav-middle-mobile.flexslider a.flex-prev, .nav-middle-mobile.flexslider a.flex-next {
        top: 50% !important;
    }

    .nav-middle-mobile.flexslider {
        margin-bottom: 0 !important;
    }

    .flexslider .slides > li > .img-box > span > img {
        height: auto;
    }

    .flexslider.carousel.nav-thumb .slides li {
        min-height: 100px;
    }

        .flexslider.carousel.nav-thumb .slides li .thumb {
            height: 100px;
            padding: 37px 7px;
        }

    .flexslider.carousel .slides li .thumb {
        height: 150px;
    }


    .section-slider > .flexslider .slides li, .flexslider .slides img {
        height: 100%;
    }

    .flexslider .slides .advs-box-side-img img, .section-slider  .flexslider .slides img  {
        height: auto;
    }

    .section-slider .overlaybox {
        top: 10px;
    }



    .header-slider .flexslider .slides, .header-slider .flexslider .slides li {
        height: 100%;
        min-height: 200px;
    }

        .header-slider .flexslider .slides li img {
            height: auto;
        }

    .header-slider, .header-slider .flexslider {
        height: 350px;
    }

        .layer-parallax, .header-slider .layer-parallax .flexslider, .layer-parallax .flexslider .slides {
            height: 350px;
        }

        .header-slider .flexslider .slides li, .header-slider.full-screen-title .slides, .header-slider.header-parallax .slides {
            height: 100%;
        }

        .header-slider.full-screen-title .flexslider, .full-screen-title.header-parallax .flexslider {
            height: 100% !important;
        }

    .header-parallax {
        height: 300px;
    }


    .header-slider .flex-control-nav {
        bottom: 10px;
    }

    .flex-control-nav.nav-inner {
        bottom: 13px;
    }

    .section-slide .container-middle, .section-slide .container-inner {
        text-align: center;
    }

    .section-slide .vertical-row > .row {
        min-width: 100%;
        width: auto;
    }
}

@media (min-width: 769px) and (max-width: 1200px) {
    .flexslider.outer-navs a.flex-prev {
        transform: translateX(-50px);
        width: 30px;
    }

    .flexslider.outer-navs a.flex-next {
        transform: translateX(50px);
        width: 30px;
    }

    .flex-direction-nav a:before {
        font-size: 35px;
    }
}
