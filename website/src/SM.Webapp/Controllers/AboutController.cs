using AspNetCore.Mvc.Routing.Localization.Attributes;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Contents.Contracts;
using SM.WebApp.Controllers;
using SM.Webapp.Core;
using SM.Webapp.Resources;

namespace SM.Webapp.Controllers;

[LocalizedRoute("en", "about")]
[LocalizedRoute("tr", "hakkimizda")]
[LocalizedRoute("ru", "о-компании")]
public class AboutController : WebController
{
    public AboutController(ILogger<CatalogController> logger, IContentService contentService) : base(contentService,
        logger)
    {
    }

    public async Task<IActionResult> Index()
    {
        return await ReturnViewAsync(async () =>
        {
            var response = _contentService.GetAboutContent(CurrentCultureInfo).Result;
            ViewBag.Languages = response.Languages.prepareLanguageDropdown();
            ViewBag.PageTitle = PageTitle(LocalizerRes.seo_page_about_title);
            ViewBag.PageDescription = PageDescription(LocalizerRes.seo_page_about_desc);
            ViewBag.PageKeywords = PageKeywords(LocalizerRes.seo_page_about_keywords);
            ViewBag.RelCanonicals = response.Languages;

            // About page specific social media meta tags
            ViewBag.OgTitle = OgTitle(LocalizerRes.seo_page_about_title);
            ViewBag.OgDescription = OgDescription(LocalizerRes.seo_page_about_desc);
            ViewBag.OgType = OgType("article");
            ViewBag.TwitterTitle = TwitterTitle(LocalizerRes.seo_page_about_title);
            ViewBag.TwitterDescription = TwitterDescription(LocalizerRes.seo_page_about_desc);

            // Hreflang URLs for About page
            ViewBag.HreflangUrls = PrepareHreflangUrls(response.Languages);
            return response;
        }, "Index");
    }
}