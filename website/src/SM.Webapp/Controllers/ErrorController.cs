using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Contents.Contracts;
using SM.Webapp.Core;

namespace SM.Webapp.Controllers;

[Route("error")]
public class ErrorController : WebController
{
    public ErrorController(ILogger<ErrorController> logger, IContentService contentService) : base(contentService,
        logger)
    {
    }

    public IActionResult Index()
    {
        var exceptionData = HttpContext.Features.Get<IExceptionHandlerPathFeature>();
        ViewBag.Message = "There is an exception!";
        _logger.LogError("Exception Url: {url}, Exception Message: {message}",
            exceptionData?.Path, exceptionData?.Error.Message);

        return View();
    }

    [Route("Error/{statusCode}")]
    public IActionResult HandleErrorCode(int statusCode)
    {
        var statusCodeData = HttpContext.Features.Get<IStatusCodeReExecuteFeature>();
        switch (statusCode)
        {
            case 404:
                ViewBag.Message = "Page Not Found!";
                break;
            case 500:
                ViewBag.Message = "Internal Server Error!";
                break;
        }

        _logger.LogError("Status Code:{statusCode}, Request Path: {path}",
            statusCode, statusCodeData.OriginalPath);

        return View();
    }
}