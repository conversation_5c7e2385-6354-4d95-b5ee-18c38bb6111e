using AspNetCore.Mvc.Routing.Localization.Attributes;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Catalog.Contracts;
using SM.Service.Catalog.DTOs;
using SM.Service.Contents.Contracts;
using SM.Webapp.Core;

namespace SM.WebApp.Controllers;

// Old routes for backward compatibility
[LocalizedRoute("en", "catalog")]
[LocalizedRoute("tr", "katalog")]
[LocalizedRoute("ru", "каталог")]
// New SEO-friendly routes
[LocalizedRoute("en", "products")]
[LocalizedRoute("tr", "urunler")]
[LocalizedRoute("ru", "продукты")]
public class CatalogController : WebController
{
    private readonly ICatalogService CatalogService;
    private readonly ILogger<CatalogController> _logger;

    public CatalogController(ILogger<CatalogController> logger, ICatalogService catalogService,
        IContentService contentService) : base(contentService, logger)
    {
        _logger = logger;
        CatalogService = catalogService;
    }

    // New SEO-friendly routes - direct access without "catalog" prefix
    [LocalizedRoute("en", "{slug}")]
    [LocalizedRoute("tr", "{slug}")]
    [LocalizedRoute("ru", "{slug}")]
    // Old routes for backward compatibility
    [LocalizedRoute("en", "products/{slug?}")]
    [LocalizedRoute("tr", "urunler/{slug?}")]
    [LocalizedRoute("ru", "продукты/{slug?}")]
    [ResponseCache(Location = ResponseCacheLocation.Any,Duration =120)]
    public async Task<IActionResult> Products()
    {
        return await ReturnViewAsync(async () =>
        {
            var response = CatalogService.GetProducts(new GetProductsRequest
            {
                CurrentCultureInfo = CurrentCultureInfo,
                Slug = Slug
            }).Result;

            ViewBag.Languages = response.Languages.prepareLanguageDropdown();
            ViewBag.PageTitle = PageTitle(response.PageContent.SeoTitle);
            ViewBag.PageDescription = PageDescription(response.PageContent.SeoDescription);
            ViewBag.PageKeywords = PageKeywords(response.PageContent.SeoKeywords);
            ViewBag.RelCanonicals = response.Languages;

            // Products page specific social media meta tags
            ViewBag.OgTitle = OgTitle(response.PageContent.SeoTitle);
            ViewBag.OgDescription = OgDescription(response.PageContent.SeoDescription);
            ViewBag.OgType = OgType("website");
            ViewBag.TwitterTitle = TwitterTitle(response.PageContent.SeoTitle);
            ViewBag.TwitterDescription = TwitterDescription(response.PageContent.SeoDescription);

            // Hreflang URLs for Products page
            ViewBag.HreflangUrls = PrepareHreflangUrls(response.Languages);
            return response;
        }, "Products");
    }

    // New SEO-friendly routes with category hierarchy
    [LocalizedRoute("en", "{categorySlug}/{slug}")]
    [LocalizedRoute("tr", "{categorySlug}/{slug}")]
    [LocalizedRoute("ru", "{categorySlug}/{slug}")]
    // Old routes for backward compatibility
    [LocalizedRoute("en", "product-detail/{slug}")]
    [LocalizedRoute("tr", "urun-detay/{slug}")]
    [LocalizedRoute("ru", "деталь-изделия/{slug}")]
    [ResponseCache(Location = ResponseCacheLocation.Any,Duration =120)]
    public async Task<IActionResult> Product(string categorySlug = null)
    {
        return await ReturnViewAsync(async () =>
        {
            var response = CatalogService.GetProductDetail(new GetProductDetailRequest
            {
                CurrentCultureInfo = CurrentCultureInfo,
                Slug = Slug
            }).Result;

            ViewBag.Languages = response.Languages.prepareLanguageDropdown();
            ViewBag.PageTitle = PageTitle(response.PageContent.SeoTitle);
            ViewBag.PageDescription = PageDescription(response.PageContent.SeoDescription);
            ViewBag.Pagekeywords = PageKeywords(response.PageContent.SeoKeywords);
            ViewBag.RelCanonicals = response.Languages;

            // Product detail page specific social media meta tags
            var productImage = response.Detail.ProductImages?.FirstOrDefault()?.ImagePath;
            ViewBag.OgTitle = OgTitle(response.Detail.Caption);
            ViewBag.OgDescription = OgDescription(response.Detail.Description);
            ViewBag.OgImage = OgImage(productImage);
            ViewBag.OgType = OgType("product");
            ViewBag.TwitterCard = TwitterCard("summary_large_image");
            ViewBag.TwitterTitle = TwitterTitle(response.Detail.Caption);
            ViewBag.TwitterDescription = TwitterDescription(response.Detail.Description);
            ViewBag.TwitterImage = TwitterImage(productImage);

            // Hreflang URLs for Product detail page
            ViewBag.HreflangUrls = PrepareHreflangUrls(response.Languages);
            return response;
        }, "Product");
    }

    /// <summary>
    /// Dynamic route handler that determines if the slug is a category or product
    /// URL patterns: /{culture}/{categorySlug} or /{culture}/{categorySlug}/{productSlug}
    /// </summary>
    [ResponseCache(Location = ResponseCacheLocation.Any, Duration = 120)]
    public async Task<IActionResult> Dynamic(string slug, string productSlug = null)
    {
        try
        {
            // If productSlug is provided, this is a product detail request
            if (!string.IsNullOrEmpty(productSlug))
            {
                // Set the slug to productSlug for the Product action
                RouteData.Values["slug"] = productSlug;
                return await Product(slug); // slug is categorySlug here
            }

            // If only slug is provided, this could be either a category or a product
            // First, try to find it as a category
            try
            {
                var categoryResponse = await CatalogService.GetProducts(new GetProductsRequest
                {
                    CurrentCultureInfo = CurrentCultureInfo,
                    Slug = slug
                });

                // If we found a category, return the Products view
                ViewBag.Languages = categoryResponse.Languages.prepareLanguageDropdown();
                ViewBag.PageTitle = PageTitle(categoryResponse.PageContent.SeoTitle);
                ViewBag.PageDescription = PageDescription(categoryResponse.PageContent.SeoDescription);
                ViewBag.PageKeywords = PageKeywords(categoryResponse.PageContent.SeoKeywords);
                ViewBag.RelCanonicals = categoryResponse.Languages;

                // Products page specific social media meta tags
                ViewBag.OgTitle = OgTitle(categoryResponse.PageContent.SeoTitle);
                ViewBag.OgDescription = OgDescription(categoryResponse.PageContent.SeoDescription);
                ViewBag.OgType = OgType("website");
                ViewBag.TwitterTitle = TwitterTitle(categoryResponse.PageContent.SeoTitle);
                ViewBag.TwitterDescription = TwitterDescription(categoryResponse.PageContent.SeoDescription);

                // Hreflang URLs for Products page
                ViewBag.HreflangUrls = PrepareHreflangUrls(categoryResponse.Languages);

                return View("Products", categoryResponse);
            }
            catch
            {
                // If category not found, try as a product (legacy single-slug product URLs)
                try
                {
                    var productResponse = await CatalogService.GetProductDetail(new GetProductDetailRequest
                    {
                        CurrentCultureInfo = CurrentCultureInfo,
                        Slug = slug
                    });

                    ViewBag.Languages = productResponse.Languages.prepareLanguageDropdown();
                    ViewBag.PageTitle = PageTitle(productResponse.PageContent.SeoTitle);
                    ViewBag.PageDescription = PageDescription(productResponse.PageContent.SeoDescription);
                    ViewBag.Pagekeywords = PageKeywords(productResponse.PageContent.SeoKeywords);
                    ViewBag.RelCanonicals = productResponse.Languages;

                    // Product detail page specific social media meta tags
                    var productImage = productResponse.Detail.ProductImages?.FirstOrDefault()?.ImagePath;
                    ViewBag.OgTitle = OgTitle(productResponse.Detail.Caption);
                    ViewBag.OgDescription = OgDescription(productResponse.Detail.Description);
                    ViewBag.OgImage = OgImage(productImage);
                    ViewBag.OgType = OgType("product");
                    ViewBag.TwitterCard = TwitterCard("summary_large_image");
                    ViewBag.TwitterTitle = TwitterTitle(productResponse.Detail.Caption);
                    ViewBag.TwitterDescription = TwitterDescription(productResponse.Detail.Description);
                    ViewBag.TwitterImage = TwitterImage(productImage);

                    // Hreflang URLs for Product detail page
                    ViewBag.HreflangUrls = PrepareHreflangUrls(productResponse.Languages);

                    return View("Product", productResponse);
                }
                catch
                {
                    // Neither category nor product found
                    return NotFound();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Dynamic action for slug: {Slug}, productSlug: {ProductSlug}", slug, productSlug);
            return NotFound();
        }
    }
}