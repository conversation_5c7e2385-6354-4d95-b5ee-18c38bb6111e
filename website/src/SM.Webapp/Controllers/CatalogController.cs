using AspNetCore.Mvc.Routing.Localization.Attributes;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Catalog.Contracts;
using SM.Service.Catalog.DTOs;
using SM.Service.Contents.Contracts;
using SM.Webapp.Core;

namespace SM.WebApp.Controllers;

// Old routes for backward compatibility
[LocalizedRoute("en", "catalog")]
[LocalizedRoute("tr", "katalog")]
[LocalizedRoute("ru", "каталог")]
// New SEO-friendly routes
[LocalizedRoute("en", "products")]
[LocalizedRoute("tr", "urunler")]
[LocalizedRoute("ru", "продукты")]
public class CatalogController : WebController
{
    private readonly ICatalogService CatalogService;

    public CatalogController(ILogger<CatalogController> logger, ICatalogService catalogService,
        IContentService contentService) : base(contentService, logger)
    {
        CatalogService = catalogService;
    }

    // New SEO-friendly routes - direct access without "catalog" prefix
    [LocalizedRoute("en", "{slug}")]
    [LocalizedRoute("tr", "{slug}")]
    [LocalizedRoute("ru", "{slug}")]
    // Old routes for backward compatibility
    [LocalizedRoute("en", "products/{slug?}")]
    [LocalizedRoute("tr", "urunler/{slug?}")]
    [LocalizedRoute("ru", "продукты/{slug?}")]
    [ResponseCache(Location = ResponseCacheLocation.Any,Duration =120)]
    public async Task<IActionResult> Products()
    {
        return await ReturnViewAsync(async () =>
        {
            var response = CatalogService.GetProducts(new GetProductsRequest
            {
                CurrentCultureInfo = CurrentCultureInfo,
                Slug = Slug
            }).Result;

            ViewBag.Languages = response.Languages.prepareLanguageDropdown();
            ViewBag.PageTitle = PageTitle(response.PageContent.SeoTitle);
            ViewBag.PageDescription = PageDescription(response.PageContent.SeoDescription);
            ViewBag.PageKeywords = PageKeywords(response.PageContent.SeoKeywords);
            ViewBag.RelCanonicals = response.Languages;

            // Products page specific social media meta tags
            ViewBag.OgTitle = OgTitle(response.PageContent.SeoTitle);
            ViewBag.OgDescription = OgDescription(response.PageContent.SeoDescription);
            ViewBag.OgType = OgType("website");
            ViewBag.TwitterTitle = TwitterTitle(response.PageContent.SeoTitle);
            ViewBag.TwitterDescription = TwitterDescription(response.PageContent.SeoDescription);

            // Hreflang URLs for Products page
            ViewBag.HreflangUrls = PrepareHreflangUrls(response.Languages);
            return response;
        }, "Products");
    }

    // New SEO-friendly routes with category hierarchy
    [LocalizedRoute("en", "{categorySlug}/{slug}")]
    [LocalizedRoute("tr", "{categorySlug}/{slug}")]
    [LocalizedRoute("ru", "{categorySlug}/{slug}")]
    // Old routes for backward compatibility
    [LocalizedRoute("en", "product-detail/{slug}")]
    [LocalizedRoute("tr", "urun-detay/{slug}")]
    [LocalizedRoute("ru", "деталь-изделия/{slug}")]
    [ResponseCache(Location = ResponseCacheLocation.Any,Duration =120)]
    public async Task<IActionResult> Product(string categorySlug = null)
    {
        return await ReturnViewAsync(async () =>
        {
            var response = CatalogService.GetProductDetail(new GetProductDetailRequest
            {
                CurrentCultureInfo = CurrentCultureInfo,
                Slug = Slug
            }).Result;

            ViewBag.Languages = response.Languages.prepareLanguageDropdown();
            ViewBag.PageTitle = PageTitle(response.PageContent.SeoTitle);
            ViewBag.PageDescription = PageDescription(response.PageContent.SeoDescription);
            ViewBag.Pagekeywords = PageKeywords(response.PageContent.SeoKeywords);
            ViewBag.RelCanonicals = response.Languages;

            // Product detail page specific social media meta tags
            var productImage = response.Detail.ProductImages?.FirstOrDefault()?.ImagePath;
            ViewBag.OgTitle = OgTitle(response.Detail.Caption);
            ViewBag.OgDescription = OgDescription(response.Detail.Description);
            ViewBag.OgImage = OgImage(productImage);
            ViewBag.OgType = OgType("product");
            ViewBag.TwitterCard = TwitterCard("summary_large_image");
            ViewBag.TwitterTitle = TwitterTitle(response.Detail.Caption);
            ViewBag.TwitterDescription = TwitterDescription(response.Detail.Description);
            ViewBag.TwitterImage = TwitterImage(productImage);

            // Hreflang URLs for Product detail page
            ViewBag.HreflangUrls = PrepareHreflangUrls(response.Languages);
            return response;
        }, "Product");
    }
}