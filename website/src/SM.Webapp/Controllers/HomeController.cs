﻿using System.Net;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Contents.Contracts;
using SM.Webapp.Core;
using SM.Webapp.Resources;

namespace SM.Webapp.Controllers;

public class HomeController : WebController
{
    public HomeController(ILogger<HomeController> logger, IContentService contentService) : base(contentService, logger)
    {
    }

    public async Task<IActionResult> Index()
    {
        return await ReturnViewAsync(async () => { return 1; }, "Index");
    }

    public IActionResult ComingSoon()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("/500")]
    public IActionResult Error(int code)
    {
        var languages = _contentService.GetLanguages(RouteData.GetLanguage()).Result;
        ViewBag.Languages = languages.prepareLanguageDropdown();
        ViewBag.StatusCode = (int)HttpStatusCode.InternalServerError;
        ViewBag.ErrorMessage = LocalizerRes.error_message_500;
        HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
        return View("Error");
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("/404")]
    public IActionResult NotFound(int code)
    {
        var languages = _contentService.GetLanguages(RouteData.GetLanguage()).Result;
        ViewBag.Languages = languages.prepareLanguageDropdown();
        ViewBag.StatusCode = (int)HttpStatusCode.NotFound;
        ViewBag.ErrorMessage = LocalizerRes.error_message_404;
        HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;

        return View("Error");
    }
}