﻿@using SM.Webapp.Resources
@await Component.InvokeAsync("HomeSlider")

@section Styles
{
    <!-- Plugins/Components CSS -->
    <link rel="stylesheet" href="/css/swiper.css">
}

@section StructuredData
{
    <!-- Organization Schema for Homepage -->
    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "Organization",
        "name": "@Html.Raw(@LocalizerRes.page_meta_title_brand)",
        "alternateName": "Sabun Mutfağı",
        "url": "@("https://www.sabunmutfagi.com/" + LocalizerRes.language)",
        "logo": "https://www.sabunmutfagi.com/images/logos/logo.png",
        "description": "@Html.Raw(ViewBag.PageDescription)",
        "slogan": "@Html.Raw(LocalizerRes.seo_page_main_slogan)",
        "foundingDate": "2011",
        "telephone": "+90-264-241-80-09",
        "email": "<EMAIL>",
        "priceRange": "₺₺",
        "address": {
            "@@type": "PostalAddress",
            "streetAddress": "Dilmen Mah. Akşemsettin cad no: 8",
            "addressLocality": "Erenler", 
            "addressRegion": "Sakarya",
            "addressCountry": "TR",
            "postalCode": "54000"
          },
        "geo": {
            "@@type": "GeoCoordinates",
            "latitude": "40.75872692461123",
            "longitude": "30.395902297357637"
          },
        "areaServed": ["TR", "EU", "RU"],
        "contactPoint": [
            {
              "@@type": "ContactPoint",
              "contactType": "customer service",
              "telephone": "+90-264-241-80-09",
              "email": "<EMAIL>",
              "availableLanguage": ["Turkish", "English", "Russian"],
              "areaServed": ["TR", "EU", "RU"]
            },
            {
              "@@type": "ContactPoint", 
              "contactType": "sales",
              "url": "https://telegram.me/sabun_mutfagi",
              "availableLanguage": ["Turkish", "English", "Russian"]
            }
          ],
        "openingHours": ["Mo-Fr 09:00-18:00"],
        "sameAs": [
            @if (ViewBag.SocialMediaAccountAddresses != null)
            {
                @for (int i = 0; i < ((IEnumerable<SM.Service.Contents.DTOs.SocialMediaAccountAddressDto>)ViewBag.SocialMediaAccountAddresses).Count(); i++)
                {
                    var socialAccount = ((IEnumerable<SM.Service.Contents.DTOs.SocialMediaAccountAddressDto>)ViewBag.SocialMediaAccountAddresses).ElementAt(i);
                    <text>"@socialAccount.Url"@(i < ((IEnumerable<SM.Service.Contents.DTOs.SocialMediaAccountAddressDto>)ViewBag.SocialMediaAccountAddresses).Count() - 1 ? "," : "")</text>
                }
            }
        ]
    }
    </script>
}

<!-- Content ============================================= -->
<section id="content">
    <div class="content-wrap pb-1">

        @await Component.InvokeAsync("Brochure")

        @await Component.InvokeAsync("HomeFeatures")

        @await Component.InvokeAsync("HomeAbout")

    </div>
</section><!-- #content end -->