@model HeaderViewModel

<!-- Header
============================================= -->
<header id="header" class="header-size-custom" data-sticky-shrink="true">
    <div id="header-wrap">
        <div class="container">
            <div class="header-row justify-content-lg-between">

                <!-- Logo
                ============================================= -->
                <div id="logo" class="mx-lg-auto col-auto flex-column order-lg-2 px-0">
                    <a href="/">
                        <img class="logo-default" srcset="/images/logos/logo.png, /images/logos/logo.png 2x" src="/images/logos/logo.png" alt="Sabun Mutfagi">

                        <img class="logo-mobile" srcset="/images/logos/logo.png, /images/logos/logo.png 2x" src="/images/logos/logo.png" alt="Sabun Mutfagi">
                    </a>
                </div><!-- #logo end -->

                <div class="col-auto col-lg-3 order-lg-1 d-none d-md-flex px-0">
                    <div class="social-icons">
                        @foreach (var m in Model.SocialMediaAccountAddresses)
                        {
                            <a href="@m.Url" class="social-icon rounded-circle si-mini @m.BgIcon" target="_blank">
                                <i class="fa-brands @m.Icon"></i>
                                <i class="fa-brands @m.Icon"></i>
                            </a>
                        }
                    </div>
                </div>

                <div class="header-misc col-auto col-lg-3 justify-content-lg-end ms-0 ms-sm-3 px-0">

                    <!-- Bookmark
                    ============================================= -->
                    @Html.Raw(ViewBag.Languages)

                </div>

                <div class="primary-menu-trigger">
                    <button class="cnvs-hamburger" type="button" title="Open Mobile Menu">
                        <span class="cnvs-hamburger-box">
                            <span class="cnvs-hamburger-inner"></span>
                        </span>
                    </button>
                </div>

            </div>
        </div>

        <div class="container">
            <div class="header-row justify-content-lg-center header-border">

                <!-- Primary Navigation
                ============================================= -->
                <nav class="primary-menu with-arrows">
                    @Model.NavigationHTML
                </nav><!-- #primary-menu end -->

            </div>
        </div>
    </div>
    <div class="header-wrap-clone"></div>

</header><!-- #header end -->