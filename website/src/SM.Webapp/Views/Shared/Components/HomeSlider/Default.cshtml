@model HomeSliderModel


<!-- Slider
============================================= -->
<section id="slider" class="slider-element slider-parallax swiper_wrapper vh-75">
    <div class="slider-inner">

        <div class="swiper swiper-parent">
            <div class="swiper-wrapper">
                @foreach (var m in Model.SliderItems)
                {
                    <div class="swiper-slide">
                        <div class="container">
                            <div class="slider-caption slider-caption-center">
                                <h2 data-animate="fadeInUp">@m.Title</h2>
                                <p class="d-none d-sm-block" data-animate="fadeInUp" data-delay="200">@m.Description</p>
                            </div>
                        </div>
                        <div class="swiper-slide-bg" style="background-image: url('@m.ImagePath');"></div>
                    </div>
                }
            </div>
            <div class="slider-arrow-left">
                <i class="uil uil-angle-left-b"></i>
            </div>
            <div class="slider-arrow-right">
                <i class="uil uil-angle-right-b"></i>
            </div>/div>
            <div class="slide-number">
                <div class="slide-number-current"></div><span>/</span>
                <div class="slide-number-total"></div>
            </div>
        </div>

    </div>
</section>
<!-- #slider end -->