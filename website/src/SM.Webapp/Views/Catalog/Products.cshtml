@using SM.Webapp.Resources
@using SM.Webapp.Core
@model SM.Service.Catalog.DTOs.GetProductsResponse

@section StructuredData
{
    <!-- CollectionPage Schema for Category/Products Page -->
    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "CollectionPage",
        "name": "@Html.Raw(ViewBag.PageTitle)",
        "description": "@Html.Raw(ViewBag.PageDescription)",
        "url": "@("https://www.sabunmutfagi.com" + Context.Request.Path)",
        "mainEntity": {
            "@@type": "ItemList",
            "name": "@Html.Raw(Model.PageContent.Title)",
            "description": "@Html.Raw(Model.PageContent.Description)",
            "numberOfItems": @Model.Products.Count(),
            "itemListElement": [
                @for (int i = 0; i < Model.Products.Count(); i++)
                {
                    var product = Model.Products.ElementAt(i);
                    <text>{
                        "@@type": "ListItem",
                        "position": @(i + 1),
                        "item": {
                            "@@type": "Product",
                            "name": "@Html.Raw(product.Caption?.Replace("&amp;", "&"))",
                            "image": "@("https://www.sabunmutfagi.com" + product.ImagePath)",
                            "url": "@("https://www.sabunmutfagi.com" + product.DetailUrl)",
                            "brand": {
                                "@@type": "Brand",
                                "name": "@Html.Raw(LocalizerRes.page_meta_title_brand)"
                            },
                            "category": "@Html.Raw(Model.PageContent.Title)"
                        }
                    }@(i < Model.Products.Count() - 1 ? "," : "")</text>
                }
            ]
        },
        "breadcrumb": {
            "@@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@@type": "ListItem",
                    "position": 1,
                    "name": "@(LocalizerRes.navigation_home)",
                    "item": "https://www.sabunmutfagi.com/@LocalizerRes.language"
                },
                {
                    "@@type": "ListItem",
                    "position": 2,
                    "name": "@Html.Raw(Model.PageContent.Title)",
                    "item": "@("https://www.sabunmutfagi.com" + Context.Request.Path)"
                }
            ]
        }
    }
    </script>
}


<!-- Page Title
============================================= -->
<section class="page-title page-title-parallax parallax scroll-detect py-lg-2">
    <img src="@Model.PageContent.ImagePath" loading="lazy" class="parallax-bg" alt="@Model.PageContent.Title"/>
    <div class="container">
        <div class="page-title-row py-5">
            <div class="page-title-content mw-xs">
                <h1>@Model.PageContent.Title</h1>
                <h2 class="color text-larger mt-4 mb-0">@Model.PageContent.Description</h2>
            </div>

        </div>
    </div>
</section><!-- .page-title end -->

<!-- Content
============================================= -->
<section id="content">
    <div class="content-wrap pb-5">
        <div class="container">
            <div class="row mb-3 mb-md-5 justify-content-md-between">
                <div class="col-md-3 order-2 order-md-1">
                    <button type="button" class="button-filter button button-border border-color m-0 color h-bg-color h-text-light w-100">
                        <span>
                            @LocalizerRes.products_filter_show<i class="bi-arrow-down me-0 ms-2"></i>
                        </span>
                        <span>
                            <i class="bi-arrow-up"></i>@LocalizerRes.products_filter_hide
                        </span>
                    </button>
                </div>

                <div class="col-12 col-md-auto order-1 order-md-2 mb-2 mb-md-0">
                    <div id="shop-filter-sorting" class="dropdown border-0">
                        <button type="button" id="shop-filter-btn" class="button button-border border-color m-0 color h-bg-color h-text-light dropdown-toggle px-5 w-100 text-center" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="true" data-bs-offset="0, -1">
                            @LocalizerRes.products_sorting
                        </button>
                        <ul class="shop-sorting dropdown-menu dropdown-menu-end shadow-sm border-0 w-100 py-0 rounded-0" aria-labelledby="shop-filter-btn">
                            <li>
                                <a href="#" class="dropdown-item" data-sort-by="name_az">
                                    <span>@LocalizerRes.products_short_az</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="dropdown-item" data-sort-by="name_za">
                                    <span>@LocalizerRes.products_short_za</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 skincare-filter sticky-sidebar-wrap">
                    <ul class="pt-4 list-unstyled position-relative ist-unstyled items-nav shop-filter custom-filter font-body" data-container="#shop" data-active-class="active-filter">
                        <hr>
                        @foreach (var c in Model.ProductCategories)
                        {
                            <li class="mb-1 d-flex justify-content-between align-items-center">
                                <a href="@c.Slug" data-filter=".sf-face">@c.Caption</a>
                            </li>
                        }
                    </ul>
                    <hr>
                </div>

                <div class="col-md-9">
                    <div id="shop" class="row shop grid-container" data-layout="fitRows">
                        <!-- Shop Item ============================================= -->
                        @foreach (var p in Model.Products)
                        {
                            <div class="col-lg-4 col-md-6 mb-4 product">
                                <div class="grid-inner">
                                    <div class="product-image">
                                        <a href="@p.DetailUrl">
                                            <img src="@p.ImagePath" loading="lazy" alt="@p.Caption">
                                        </a>
                                        <div class="bg-overlay">
                                            <div class="bg-overlay-content align-items-end justify-content-end" data-hover-animate="fadeIn">
                                                <a href="@p.DetailUrl" class="d-block positon-absolute top-0 start-0 w-100 h-100 z-1">
                                                    <span class="visually-hidden">Product Link</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="product-desc text-center">
                                        <div class="product-title">
                                            <h3>
                                                <a href="@p.DetailUrl" class="color">@Html.Raw(p.Caption)</a>
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

    </div>
</section><!-- #content end -->

@section Scripts
{
    <script type="text/javascript" nonce="@Html.Nonce()">
    		jQuery(".button-filter").click(function(){
    			jQuery(".skincare-filter").toggleClass("skincare-filter-hide");
    			jQuery(this).toggleClass("button-filter-active-hide");
    			SEMICOLON.Modules.gridInit();
    		});
    
    		var priceRangefrom = 0,
    			priceRangeto = 0,
    			$container = jQuery('#shop');
    
    		jQuery(window).on( 'load', function(){
    
    			jQuery(window).resize(function() {
    				$container.isotope('layout');
    			});
    
    		});
    
    		jQuery(document).ready( function($){
    			jQuery(".dropdown-item span").on( 'click', function(){
    				var displayText = jQuery(this).text();
    				jQuery("#shop-filter-btn").html(displayText);
    				jQuery(this).parents('.dropdown').find('.show').toggleClass('show', false);
    			});
    		});
    
    		jQuery(window).on( 'load', function(){
    			jQuery('.shop-filter').find('li:not(.custom-filter-reset)').each( function(){
    				var element = jQuery(this),
    					elementFilter = element.children().attr('data-filter'),
    					elementFilterContainer = element.parents('.custom-filter').attr('data-container');
    
    				elementFilterCount = Number( jQuery(elementFilterContainer).find( '.product' ).filter( elementFilter ).length );
    
    				element.find('.shop-filter-count').text(elementFilterCount);
    
    			});
    
    			jQuery('#shop').isotope({
    				getSortData: {
    					name_az: '.product-title',
    					name_za: '.product-title',
    					price_lh: function( itemElem ) {
    						if( jQuery(itemElem).find('.product-price').find('ins').length > 0 ) {
    							var price = jQuery(itemElem).find('.product-price ins').text();
    						} else {
    							var price = jQuery(itemElem).find('.product-price').text();
    						}
    
    						priceNum = price.split("$");
    
    						return parseFloat( priceNum[1] );
    					},
    					price_hl: function( itemElem ) {
    						if( jQuery(itemElem).find('.product-price').find('ins').length > 0 ) {
    							var price = jQuery(itemElem).find('.product-price ins').text();
    						} else {
    							var price = jQuery(itemElem).find('.product-price').text();
    						}
    
    						priceNum = price.split("$");
    
    						return parseFloat( priceNum[1] );
    					}
    				},
    				sortAscending: {
    					name_az: true,
    					name_za: false,
    					price_lh: true,
    					price_hl: false
    				}
    			});
    
    			jQuery('.shop-sorting li').on( 'click', function() {
    				jQuery('.shop-sorting').find('li').removeClass( 'active-filter' );
    				jQuery(this).addClass( 'active-filter' );
    				var sortByValue = jQuery(this).find('a').attr('data-sort-by');
    				jQuery('#shop').isotope({ sortBy: sortByValue });
    				return false;
    			});
    		});
    	</script>
}