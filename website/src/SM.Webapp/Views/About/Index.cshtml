@using SM.Webapp.Resources
@model SM.Service.Contents.DTOs.GetAboutContentResponse

@section StructuredData
{
    <!-- AboutPage Schema for About Us Page -->
    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "AboutPage",
        "name": "@Html.Raw(ViewBag.PageTitle)",
        "description": "@Html.Raw(ViewBag.PageDescription)",
        "url": "@("https://www.sabunmutfagi.com" + Context.Request.Path)",
        "mainEntity": {
            "@@type": "Organization",
            "name": "@Html.Raw(@LocalizerRes.page_meta_title_brand)",
            "alternateName": "Sabun Mutfağı",
            "url": "@("https://www.sabunmutfagi.com/" + LocalizerRes.language)",
            "foundingDate": "2011",
            "description": "@Html.Raw(ViewBag.PageDescription)",
            "founder": {
                "@@type": "Person",
                "name": "<PERSON><PERSON>ra <PERSON>ı<PERSON>"
            },
            "address": {
              "@@type": "PostalAddress",
              "streetAddress": "Dilmen Mah. Akşemsettin cad no: 8",
              "addressLocality": "Erenler",
              "addressRegion": "Sakarya",
              "addressCountry": "TR"
            },
            "contactPoint": {
              "@@type": "ContactPoint",
              "contactType": "customer service",
              "telephone": "+90-264-241-80-09",
              "email": "<EMAIL>"
            },
            "specialty": [@Html.Raw(LocalizerRes.seo_page_about_specialty)],
            "knowsAbout": [@Html.Raw(LocalizerRes.seo_page_about_knowsAbout)]
        },
        "breadcrumb": {
            "@@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@@type": "ListItem",
                    "position": 1,
                    "name": "@(LocalizerRes.language == "tr" ? "Ana Sayfa" : LocalizerRes.language == "en" ? "Home" : "Главная")",
                    "item": "https://www.sabunmutfagi.com/@LocalizerRes.language"
                },
                {
                    "@@type": "ListItem",
                    "position": 2,
                    "name": "@Html.Raw(Model.Title)",
                    "item": "@("https://www.sabunmutfagi.com" + Context.Request.Path)"
                }
            ]
        }
    }
    </script>
}

<div id="content">
    <div class="content-wrap pt-5">
        <!-- Page Title ============================================= -->
        <div class="text-center">
            <h1 class="display-5 mb-1">@Model.Title</h1>
        </div>
        <div class="section mt-0 mb-0 pb-0">
            <div class="container">
                <div class="row align-items-center flex-md-row-reverse col-mb-50 mb-0">
                    <div class="col-lg-6">
                        <img class="box-img shadow-left" src="/images/contents/image-4.jpg" loading="lazy" alt="@Html.Raw(LocalizerRes.about_image_production_alt)">
                    </div>
                    <div class="col-lg-6 pe-0 pe-md-5">
                        @Html.Raw(Model.AboutTextPart1)
                    </div>
                </div>

                <div class="clear"></div>

                <div class="row align-items-center mt-0 col-mb-50">
                    <div class="col-lg-6">
                        <img class="box-img" src="/images/contents/image-2.jpg" loading="lazy" alt="@Html.Raw(LocalizerRes.about_image_workshop_alt)">
                    </div>
                    <div class="col-lg-6 ps-md-5 mb-5 mb-md-0">
                        @Html.Raw(Model.AboutTextPart2)
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>