using System.Globalization;
using System.Security.Cryptography;
using AspNetCore.Mvc.Routing.Localization.Extensions;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.EntityFrameworkCore;
using SM.Data;
using SM.Localization;
using SM.Service;
using SM.Webapp;
using SM.Webapp.Middleware;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddDbContext<SMDbContext>(optionsBuilder =>
    optionsBuilder.UseSqlServer(
        "data source=***************\\MSSQLSERVER2022;initial catalog=sabunmutfagi_prod;user=sabunmutfagi_website;password=***************;MultipleActiveResultSets=True;TrustServerCertificate=True;"));

builder.Services.AddServiceModule();

var supportedCultures = new[]
{
    new CultureInfo("tr"),
    new CultureInfo("en"),
    new CultureInfo("ru"),
    new CultureInfo("de"),
    new CultureInfo("ar")
};

builder.Services.AddLocalizedRouting(supportedCultures);
builder.Services.AddSingleton<LocalizedRoutingTranslationTransformer>();
//builder.Services.AddLocalization();
builder.Services.AddLocalization(options => { options.ResourcesPath = "Resources"; });

// Add services to the container.
builder.Services.AddControllersWithViews()
    .AddViewLocalization(LanguageViewLocationExpanderFormat.Suffix);

builder.Services.AddTransient<ErrorHandlerMiddleware>();
// builder.Services.AddScoped<ExceptionFilter>();
// builder.Services.AddScoped<GlobalActionFilter>();

builder.Services.AddResponseCaching();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/Error");
    //app.UseStatusCodePages();
    //app.UseStatusCodePagesWithRedirects("/Error");
    app.UseStatusCodePagesWithReExecute("/Error/{0}");

    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseStatusCodePagesWithRedirects("/Error");

app.Use(async (context, next) => {
    
    var rng = new RNGCryptoServiceProvider();
    var nonceBytes = new byte[32];
    rng.GetBytes(nonceBytes);
    var nonce = Convert.ToBase64String(nonceBytes);
    context.Items.Add("ScriptNonce", nonce);
    context.Response.Headers.Add("Content-Security-Policy",
        new[] { string.Format("script-src 'self' 'nonce-{0}'", nonce) });
    
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Remove("X-Powered-By");
    
    if (context.Request.Path.StartsWithSegments("/robots.txt") || context.Request.Path.StartsWithSegments("/Robots.txt")) {
        var robotsTxtPath = "Robots.txt";
        string output = "User-agent: *  \nAllow: /";
        if (File.Exists(robotsTxtPath)) {
            output = await File.ReadAllTextAsync(robotsTxtPath);
        }
        context.Response.ContentType = "text/plain";
        await context.Response.WriteAsync(output);
    }else if (context.Request.Path.StartsWithSegments("/sitemap.xml") || context.Request.Path.StartsWithSegments("/sitemap.xml")) {
        var robotsTxtPath = "sitemap.xml";
        string output = "";
        if (File.Exists(robotsTxtPath)) {
            output = await File.ReadAllTextAsync(robotsTxtPath);
        }
        context.Response.ContentType = "xml";
        await context.Response.WriteAsync(output);
    }
    else
    {
        await next();
    }
});


app.UseHttpsRedirection();
app.UseStaticFiles();

// global error handler
app.UseMiddleware<ErrorHandlerMiddleware>();
// URL redirect middleware for SEO-friendly URLs
app.UseMiddleware<UrlRedirectMiddleware>();

app.UseRouting();
app.UseLocalizedRouting("tr", supportedCultures);
app.UseAuthorization();

// SEO-friendly catalog routes - dynamic resolution (higher priority)
app.MapControllerRoute(
    name: "CatalogDynamic",
    pattern: "{culture=tr}/{slug}/{productSlug?}",
    defaults: new { controller = "Catalog", action = "Dynamic" });

app.MapControllerRoute(
    name: "CatalogDynamicEn",
    pattern: "{culture=en}/{slug}/{productSlug?}",
    defaults: new { controller = "Catalog", action = "Dynamic" });

app.MapControllerRoute(
    name: "CatalogDynamicRu",
    pattern: "{culture=ru}/{slug}/{productSlug?}",
    defaults: new { controller = "Catalog", action = "Dynamic" });

// Dynamic localized routing (lower priority)
app.MapDynamicControllerRoute<LocalizedRoutingTranslationTransformer>(
    "{culture=tr}/{controller=Home}/{action=Index}/{slug?}");

app.MapControllerRoute(
    "default",
    "{culture=tr}/{controller=Home}/{action=Index}/{slug?}");

app.UseResponseCaching();


app.Run();