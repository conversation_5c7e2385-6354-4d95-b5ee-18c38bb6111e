using System.Text;
using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc.Rendering;
using SM.Service.Contents.DTOs;

namespace SM.Webapp.Core;

public static class Extensions
{
    public static string GetLanguage(this RouteData routeData)
    {
        var value = routeData.Values["culture"];
        return (value != null ? value.ToString() : "tr") ?? string.Empty;
    }

    public static string GetSlug(this RouteData routeData)
    {
        var value = routeData.Values["slug"];
        return value != null ? value.ToString() : "";
    }

    public static string GetUri(this HttpRequest request)
    {
        var uriBuilder = new UriBuilder
        {
            Scheme = request.Scheme,
            Host = request.Host.Host,
            Port = request.Host.Port.GetValueOrDefault(80),
            Path = request.Path.ToString(),
            Query = request.QueryString.ToString()
        };
        return uriBuilder.Uri.ToString();
    }

    public static string prepareLanguageDropdown(this IEnumerable<LanguageDto> dto)
    {
        var output = new StringBuilder();

        output.Append("<div class=\"dropdown dropdown-langs\">");
        output.Append(
            "<button class=\"btn dropdown-toggle px-1\" type=\"button\" id=\"dropdownMenuButton\" data-bs-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\">");
        output.Append($"<img src=\"{dto.First(f => f.IsSelected).ImagePath}\" alt=\"flag\">");
        output.Append("</button>");
        output.Append("<div class=\"dropdown-menu dropdown-menu-end\" aria-labelledby=\"dropdownMenuButton\">");

        foreach (var m in dto)
            if (m.IsSelected)
            {
                output.Append(
                    $"<a href=\"{m.Url}\" class=\"dropdown-item disabled\" tabindex=\" - 1\" aria-disabled=\"true\">");
                output.Append($"<img src=\"{m.ImagePath}\" alt=\"Lang\"> {m.Name}</a>");
            }
            else
            {
                output.Append(
                    $"<a href=\"{m.Url}\" class=\"dropdown-item\"><img src=\"{m.ImagePath}\" alt=\"Lang\"> {m.Name}</a>");
            }

        output.Append("</div> </div>");
        return output.ToString();
    }
    
    public static HtmlString Nonce(this IHtmlHelper helper)
    {
        string nonceValue = helper.ViewContext.HttpContext.Items["ScriptNonce"].ToString();
        return new HtmlString(nonceValue);
    }
}