using System.Text.RegularExpressions;
using SM.Service.Catalog.Contracts;

namespace SM.Webapp.Middleware;

public class UrlRedirectMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<UrlRedirectMiddleware> _logger;
    private readonly IServiceProvider _serviceProvider;

    public UrlRedirectMiddleware(RequestDelegate next, ILogger<UrlRedirectMiddleware> logger, IServiceProvider serviceProvider)
    {
        _next = next;
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        
        if (!string.IsNullOrEmpty(path))
        {
            var redirectUrl = await GetRedirectUrlAsync(path);

            if (!string.IsNullOrEmpty(redirectUrl))
            {
                _logger.LogInformation($"Redirecting from {path} to {redirectUrl}");
                context.Response.Redirect(redirectUrl, permanent: true);
                return;
            }
        }

        await _next(context);
    }

    private async Task<string> GetRedirectUrlAsync(string path)
    {
        // Old catalog URLs to new SEO-friendly URLs

        // Turkish product detail redirects with real category lookup
        if (Regex.IsMatch(path, @"^/tr/katalog/urun-detay/(.+)$"))
        {
            var match = Regex.Match(path, @"^/tr/katalog/urun-detay/(.+)$");
            var productSlug = match.Groups[1].Value;
            var categorySlug = await GetCategorySlugAsync(productSlug, "tr");
            return $"/tr/{categorySlug}/{productSlug}";
        }

        // English product detail redirects with real category lookup
        if (Regex.IsMatch(path, @"^/en/catalog/product-detail/(.+)$"))
        {
            var match = Regex.Match(path, @"^/en/catalog/product-detail/(.+)$");
            var productSlug = match.Groups[1].Value;
            var categorySlug = await GetCategorySlugAsync(productSlug, "en");
            return $"/en/{categorySlug}/{productSlug}";
        }

        // Russian product detail redirects with real category lookup
        if (Regex.IsMatch(path, @"^/ru/каталог/деталь-изделия/(.+)$"))
        {
            var match = Regex.Match(path, @"^/ru/каталог/деталь-изделия/(.+)$");
            var productSlug = match.Groups[1].Value;
            var categorySlug = await GetCategorySlugAsync(productSlug, "ru");
            return $"/ru/{categorySlug}/{productSlug}";
        }

        // Turkish category list redirects
        if (Regex.IsMatch(path, @"^/tr/katalog/urunler/(.+)$"))
        {
            var match = Regex.Match(path, @"^/tr/katalog/urunler/(.+)$");
            return $"/tr/{match.Groups[1].Value}";
        }

        // English category list redirects
        if (Regex.IsMatch(path, @"^/en/catalog/products/(.+)$"))
        {
            var match = Regex.Match(path, @"^/en/catalog/products/(.+)$");
            return $"/en/{match.Groups[1].Value}";
        }

        // Russian category list redirects
        if (Regex.IsMatch(path, @"^/ru/каталог/продукты/(.+)$"))
        {
            var match = Regex.Match(path, @"^/ru/каталог/продукты/(.+)$");
            return $"/ru/{match.Groups[1].Value}";
        }

        // Redirect old "urunler/products/продукты" URLs to new clean URLs
        if (Regex.IsMatch(path, @"^/tr/urunler/(.+)/(.+)$"))
        {
            var match = Regex.Match(path, @"^/tr/urunler/(.+)/(.+)$");
            return $"/tr/{match.Groups[1].Value}/{match.Groups[2].Value}";
        }

        if (Regex.IsMatch(path, @"^/en/products/(.+)/(.+)$"))
        {
            var match = Regex.Match(path, @"^/en/products/(.+)/(.+)$");
            return $"/en/{match.Groups[1].Value}/{match.Groups[2].Value}";
        }

        if (Regex.IsMatch(path, @"^/ru/продукты/(.+)/(.+)$"))
        {
            var match = Regex.Match(path, @"^/ru/продукты/(.+)/(.+)$");
            return $"/ru/{match.Groups[1].Value}/{match.Groups[2].Value}";
        }

        // Redirect old category URLs to new clean URLs
        if (Regex.IsMatch(path, @"^/tr/urunler/(.+)$"))
        {
            var match = Regex.Match(path, @"^/tr/urunler/(.+)$");
            return $"/tr/{match.Groups[1].Value}";
        }

        if (Regex.IsMatch(path, @"^/en/products/(.+)$"))
        {
            var match = Regex.Match(path, @"^/en/products/(.+)$");
            return $"/en/{match.Groups[1].Value}";
        }

        if (Regex.IsMatch(path, @"^/ru/продукты/(.+)$"))
        {
            var match = Regex.Match(path, @"^/ru/продукты/(.+)$");
            return $"/ru/{match.Groups[1].Value}";
        }

        return null;
    }

    private async Task<string> GetCategorySlugAsync(string productSlug, string langPrefix)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var catalogService = scope.ServiceProvider.GetRequiredService<ICatalogService>();

            return await catalogService.GetCategorySlugByProductSlugAsync(productSlug, langPrefix);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting category slug for product {ProductSlug} in language {LangPrefix}", productSlug, langPrefix);
            return GetFallbackCategorySlug(langPrefix);
        }
    }

    private static string GetFallbackCategorySlug(string langPrefix)
    {
        return langPrefix switch
        {
            "tr" => "genel",
            "en" => "general",
            "ru" => "общий",
            _ => "genel"
        };
    }
}
