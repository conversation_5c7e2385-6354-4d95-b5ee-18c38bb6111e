using System.Text.RegularExpressions;
using SM.Data;
using SM.Service.Helper;
using Microsoft.EntityFrameworkCore;

namespace SM.Webapp.Middleware;

public class UrlRedirectMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<UrlRedirectMiddleware> _logger;
    private readonly IServiceProvider _serviceProvider;

    public UrlRedirectMiddleware(RequestDelegate next, ILogger<UrlRedirectMiddleware> logger, IServiceProvider serviceProvider)
    {
        _next = next;
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        
        if (!string.IsNullOrEmpty(path))
        {
            var redirectUrl = await GetRedirectUrlAsync(path);

            if (!string.IsNullOrEmpty(redirectUrl))
            {
                _logger.LogInformation($"Redirecting from {path} to {redirectUrl}");
                context.Response.Redirect(redirectUrl, permanent: true);
                return;
            }
        }

        await _next(context);
    }

    private async Task<string> GetRedirectUrlAsync(string path)
    {
        // Old catalog URLs to new SEO-friendly URLs

        // Turkish product detail redirects with real category lookup
        if (Regex.IsMatch(path, @"^/tr/katalog/urun-detay/(.+)$"))
        {
            var match = Regex.Match(path, @"^/tr/katalog/urun-detay/(.+)$");
            var productSlug = match.Groups[1].Value;
            var categorySlug = await GetRealCategorySlugAsync(productSlug, "tr");
            return $"/tr/urunler/{categorySlug}/{productSlug}";
        }

        // English product detail redirects with real category lookup
        if (Regex.IsMatch(path, @"^/en/catalog/product-detail/(.+)$"))
        {
            var match = Regex.Match(path, @"^/en/catalog/product-detail/(.+)$");
            var productSlug = match.Groups[1].Value;
            var categorySlug = await GetRealCategorySlugAsync(productSlug, "en");
            return $"/en/products/{categorySlug}/{productSlug}";
        }

        // Russian product detail redirects with real category lookup
        if (Regex.IsMatch(path, @"^/ru/каталог/деталь-изделия/(.+)$"))
        {
            var match = Regex.Match(path, @"^/ru/каталог/деталь-изделия/(.+)$");
            var productSlug = match.Groups[1].Value;
            var categorySlug = await GetRealCategorySlugAsync(productSlug, "ru");
            return $"/ru/продукты/{categorySlug}/{productSlug}";
        }

        // Turkish category list redirects
        if (Regex.IsMatch(path, @"^/tr/katalog/urunler/(.+)$"))
        {
            var match = Regex.Match(path, @"^/tr/katalog/urunler/(.+)$");
            return $"/tr/urunler/{match.Groups[1].Value}";
        }

        // English category list redirects
        if (Regex.IsMatch(path, @"^/en/catalog/products/(.+)$"))
        {
            var match = Regex.Match(path, @"^/en/catalog/products/(.+)$");
            return $"/en/products/{match.Groups[1].Value}";
        }

        // Russian category list redirects
        if (Regex.IsMatch(path, @"^/ru/каталог/продукты/(.+)$"))
        {
            var match = Regex.Match(path, @"^/ru/каталог/продукты/(.+)$");
            return $"/ru/продукты/{match.Groups[1].Value}";
        }

        return null;
    }

    private async Task<string> GetRealCategorySlugAsync(string productSlug, string langPrefix)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<SMDbContext>();

            // Ultra-optimized single query with explicit joins and AsNoTracking
            var categorySlug = await dbContext.ProductTranslations
                .AsNoTracking()
                .Where(pt => pt.Slug == productSlug && pt.LangPrefix == langPrefix)
                .Join(dbContext.ProductCategoryRelations.AsNoTracking(),
                      pt => pt.ProductId,
                      pcr => pcr.ProductId,
                      (pt, pcr) => pcr.CategoryId)
                .Join(dbContext.ProductCategoryTranslations.AsNoTracking(),
                      categoryId => categoryId,
                      pct => pct.CategoryId,
                      (categoryId, pct) => new { pct.Slug, pct.LangPrefix })
                .Where(x => x.LangPrefix == langPrefix)
                .Select(x => x.Slug)
                .FirstOrDefaultAsync();

            return categorySlug ?? GetFallbackCategorySlug(langPrefix);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting category slug for product {ProductSlug} in language {LangPrefix}", productSlug, langPrefix);
            return GetFallbackCategorySlug(langPrefix);
        }
    }

    private static string GetFallbackCategorySlug(string langPrefix)
    {
        return langPrefix switch
        {
            "tr" => "genel",
            "en" => "general",
            "ru" => "общий",
            _ => "genel"
        };
    }
}
