using Newtonsoft.Json;

namespace SM.Webapp.Models;

public class WrapResponseModel : WrapResponseModel<object>
{
}

public class WrapResponseModel<TData>
{
    [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
    public string Message { get; set; }

    [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
    public string InternalMessage { get; set; }

    public string Code { get; set; }

    [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
    public TData Data { get; set; }

    public bool Success { get; set; }

    [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
    public List<WrapResponseModelError> Errors { get; set; }
}

public class WrapResponseModelPagedData<T>
{
    public List<T> Items { get; set; }
    public int PageCount { get; set; }
    public int ItemCount { get; set; }
    public int CurrentPage { get; set; }
}

public class WrapResponseModelError
{
    public string Message { get; set; }

    [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
    public string InternalMessage { get; set; }

    public string Name { get; set; }
}