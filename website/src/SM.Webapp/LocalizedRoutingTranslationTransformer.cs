using AspNetCore.Mvc.Routing.Localization;
using Microsoft.AspNetCore.Mvc.Routing;

namespace SM.Webapp;

public class LocalizedRoutingTranslationTransformer : DynamicRouteValueTransformer
{
    private readonly ILocalizedRoutingDynamicRouteValueResolver _localizedRoutingDynamicRouteValueResolver;

    public LocalizedRoutingTranslationTransformer(
        ILocalizedRoutingDynamicRouteValueResolver localizedRoutingDynamicRouteValueResolver)
    {
        _localizedRoutingDynamicRouteValueResolver = localizedRoutingDynamicRouteValueResolver;
    }

    public override async ValueTask<RouteValueDictionary> TransformAsync(HttpContext httpContext,
        RouteValueDictionary values)
    {
        return await _localizedRoutingDynamicRouteValueResolver.ResolveAsync(values);
    }
}