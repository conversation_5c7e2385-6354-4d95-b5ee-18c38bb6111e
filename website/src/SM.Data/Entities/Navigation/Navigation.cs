﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SM.Data.Entities.Trackable;

namespace SM.Data.Entities.Navigation;

[Table("Navigations")]
public class Navigation : FullTrackableEntry<int>
{
    [Required] public string Code { get; set; }

    [Required] public int ParentId { get; set; }

    [Required] public bool IsActive { get; set; }

    [Required] public NavigationType Type { get; set; }

    [Required] public int SortPriority { get; set; }
}