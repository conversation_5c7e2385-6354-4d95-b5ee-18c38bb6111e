﻿using System.ComponentModel.DataAnnotations.Schema;
using SM.Data.Entities.Trackable;

namespace SM.Data.Entities.Navigation;

[Table("NavigationTranslations")]
public class NavigationTranslation : FullTrackableEntry<int>
{
    public int NavigationId { get; set; }
    public string LangPrefix { get; set; }
    public string Caption { get; set; }
    public string Link { get; set; }

    [ForeignKey("NavigationId")] public virtual Navigation Navigation { get; set; }
}