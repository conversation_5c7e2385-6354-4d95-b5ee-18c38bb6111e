﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using SM.Data.Entities.Trackable;

namespace SM.Data.Entities.Content;

[Table("Contents")]
[Index(nameof(Key), IsUnique = true)]
public class Content : FullTrackableEntry<int>
{
    [Required] public string Key { get; set; }
    public string Value { get; set; }

    public ContentType Type { get; set; }
}