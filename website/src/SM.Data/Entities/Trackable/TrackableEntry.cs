﻿using System.ComponentModel.DataAnnotations;

namespace SM.Data.Entities.Trackable;

public interface ITrackableEntry<TPrimaryKey>
{
    TPrimaryKey Id { get; set; }
}

public interface ICreationTrackable
{
    public DateTime CreatedAt { get; set; }
}

public interface IModificationTrackable
{
    public DateTime? ModifiedAt { get; set; }
}

public interface IDeletionTrackable
{
    public DateTime? DeletedAt { get; set; }
}

public interface ISoftDeletable
{
    public bool IsDeleted { get; set; }
}

[Serializable]
public abstract class TrackableEntry<TPrimaryKey> : ITrackableEntry<TPrimaryKey>
{
    [Key] public virtual TPrimaryKey Id { get; set; }
}

[Serializable]
public abstract class FullTrackableEntry<TPrimaryKey> : TrackableEntry<TPrimaryKey>, ICreationTrackable,
    IModificationTrackable, IDeletionTrackable, ISoftDeletable
{
    public DateTime CreatedAt { get; set; }
    public DateTime? DeletedAt { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public bool IsDeleted { get; set; }
}