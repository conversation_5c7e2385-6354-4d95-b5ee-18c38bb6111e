using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SM.Data.Entities.Trackable;

namespace SM.Data.Entities.Message;

[Table("Messages")]
public class Message : FullTrackableEntry<int>
{
    [Required] public string FullName { get; set; }

    public string Email { get; set; }

    public string? Phone { get; set; }

    public string Text { get; set; }
}