﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SM.Data.Entities.Trackable;

namespace SM.Data.Entities.Language;

[Table("Languages")]
public class Language : FullTrackableEntry<int>
{
    [Required] public string Name { get; set; }

    [Required] public string IconPath { get; set; }

    public string Prefix { get; set; }

    public bool IsActive { get; set; }

    public string CultureInfo { get; set; }
}