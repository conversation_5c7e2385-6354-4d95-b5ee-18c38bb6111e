<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.11"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.11">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.11"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.11">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0-rc.2.22472.3"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="7.0.0-rc.1.22426.10"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0-rc.1.22426.10"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Migrations\"/>
    </ItemGroup>


</Project>
