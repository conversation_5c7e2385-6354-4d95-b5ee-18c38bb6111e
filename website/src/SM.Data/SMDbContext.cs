﻿using Microsoft.EntityFrameworkCore;
using SM.Data.Entities.Catalog;
using SM.Data.Entities.Content;
using SM.Data.Entities.Language;
using SM.Data.Entities.Message;
using SM.Data.Entities.Navigation;
using SM.Data.Entities.Trackable;
using SM.Data.Entities.User;

namespace SM.Data;

public class SMDbContext : DbContext
{
    public SMDbContext(DbContextOptions options) : base(options)
    {
    }


    public DbSet<Product> Products { get; set; }
    public DbSet<ProductTranslation> ProductTranslations { get; set; }
    public DbSet<ProductImage> ProductImages { get; set; }
    public DbSet<ProductCategory> ProductCategories { get; set; }
    public DbSet<ProductCategoryRelation> ProductCategoryRelations { get; set; }
    public DbSet<ProductCategoryTranslation> ProductCategoryTranslations { get; set; }

    public DbSet<Content> Contents { get; set; }

    public DbSet<Language> Languages { get; set; }

    public DbSet<Navigation> Navigations { get; set; }
    public DbSet<NavigationTranslation> NavigationTranslations { get; set; }

    public DbSet<User> Users { get; set; }

    public DbSet<Message> Messages { get; set; }

    public override int SaveChanges(bool acceptAllChangesOnSuccess)
    {
        OnBeforeSaving();
        return base.SaveChanges(acceptAllChangesOnSuccess);
    }

    public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
        CancellationToken cancellationToken = new())
    {
        OnBeforeSaving();
        return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
    }

    protected virtual void OnBeforeSaving()
    {
        foreach (var entry in ChangeTracker.Entries())
            switch (entry.State)
            {
                case EntityState.Added when entry.Entity is ICreationTrackable:
                    entry.Property(nameof(ICreationTrackable.CreatedAt)).CurrentValue = DateTime.UtcNow;
                    break;

                case EntityState.Deleted when entry.Entity is ISoftDeletable:
                case EntityState.Deleted when entry.Entity is IDeletionTrackable:
                    entry.State = EntityState.Unchanged;
                    entry.Property(nameof(ISoftDeletable.IsDeleted)).CurrentValue = true;
                    entry.Property(nameof(IDeletionTrackable.DeletedAt)).CurrentValue = DateTime.UtcNow;
                    break;

                case EntityState.Modified when entry.Entity is IModificationTrackable:
                    entry.Property(nameof(IModificationTrackable.ModifiedAt)).CurrentValue = DateTime.UtcNow;
                    break;
            }
    }
}