﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace SM.Data;

public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<SMDbContext>
{
    public SMDbContext CreateDbContext(string[] args)
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json")
            .Build();

        var builder = new DbContextOptionsBuilder<SMDbContext>();

        var connectionString = configuration.GetSection("Database:ConnectionString").Value;

        builder.UseSqlServer(connectionString);

        return new SMDbContext(builder.Options);
    }
}