﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AspNetCore.Mvc.Routing.Localization.Models;
using Microsoft.AspNetCore.Routing;

namespace AspNetCore.Mvc.Routing.Localization
{
    internal class LocalizedRoutingDynamicRouteValueResolver : ILocalizedRoutingDynamicRouteValueResolver
    {
        private readonly ILocalizedRoutingProvider _localizedRoutingProvider;

        public LocalizedRoutingDynamicRouteValueResolver(ILocalizedRoutingProvider localizedRoutingProvider)
        {
            _localizedRoutingProvider = localizedRoutingProvider;
        }

        public async Task<RouteValueDictionary> ResolveAsync(RouteValueDictionary values)
        {
            if (!values.TryGetValue("culture", out var culture) ||
                !values.TryGetValue("controller", out var controller) ||
                !values.TryGetValue("action", out var action)) return values;


            var routeInformationMetadata = RouteInformation.Create("Home", "NotFound");

            try
            {
                routeInformationMetadata = await _localizedRoutingProvider.ProvideRouteAsync((string)culture,
                    (string)controller, (string)action, LocalizationDirection.TranslatedToOriginal);
            }
            catch (Exception e)
            {
                if (!_localizedRoutingProvider.SupportedCultures.Any(a => a.Name.Equals(culture)))
                    values["culture"] = "tr";
            }

            values["controller"] = routeInformationMetadata.Controller;
            values["action"] = routeInformationMetadata.Action;

            return values;
        }
    }
}