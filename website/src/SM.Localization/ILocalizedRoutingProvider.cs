﻿using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using AspNetCore.Mvc.Routing.Localization.Models;

namespace AspNetCore.Mvc.Routing.Localization
{
    public interface ILocalizedRoutingProvider
    {
        internal IEnumerable<LocalizedRoute> Routes { get; }
        internal IList<CultureInfo> SupportedCultures { get; }

        Task<RouteInformation> ProvideRouteAsync(string culture, string controller, string action,
            LocalizationDirection direction);
    }
}